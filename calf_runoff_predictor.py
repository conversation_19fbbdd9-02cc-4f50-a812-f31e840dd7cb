"""
使用CALF模型进行径流预测
按照7:3划分训练集和测试集
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
import os
import argparse
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

from models.CALF import Model
from peft import get_peft_model

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class CALFRunoffPredictor:
    """使用CALF模型的径流预测器"""
    
    def __init__(self, configs):
        self.configs = configs
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        self.data = None
        self.model = None
        self.scaler = StandardScaler()
        self.results = {}
        
    def load_data(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        """加载径流数据"""
        print("正在加载径流数据...")
        self.data = pd.read_csv(data_path)
        self.data['DATE'] = pd.to_datetime(self.data['DATE'])
        self.data = self.data.sort_values('DATE').reset_index(drop=True)
        self.data = self.data.dropna()
        print(f"数据加载完成：{len(self.data)}条记录")
        
    def prepare_time_series_data(self):
        """准备时间序列数据"""
        print("正在准备时间序列数据...")
        
        # 只使用径流数据进行时间序列预测
        runoff_data = self.data['RUNOFF'].values
        
        # 创建时间序列样本
        seq_len = self.configs.seq_len
        pred_len = self.configs.pred_len
        
        X, y = [], []
        
        for i in range(len(runoff_data) - seq_len - pred_len + 1):
            # 输入序列：过去seq_len天的径流数据
            X.append(runoff_data[i:i + seq_len])
            # 输出序列：未来pred_len天的径流数据
            y.append(runoff_data[i + seq_len:i + seq_len + pred_len])
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"生成样本数量: {len(X)}")
        print(f"输入序列长度: {seq_len}, 预测序列长度: {pred_len}")
        
        # 数据标准化
        X_reshaped = X.reshape(-1, 1)
        y_reshaped = y.reshape(-1, 1)
        
        # 拟合标准化器
        all_data = np.concatenate([X_reshaped, y_reshaped], axis=0)
        self.scaler.fit(all_data)
        
        # 标准化数据
        X_scaled = self.scaler.transform(X_reshaped).reshape(X.shape)
        y_scaled = self.scaler.transform(y_reshaped).reshape(y.shape)
        
        # 按7:3划分数据集
        split_idx = int(len(X) * 0.7)
        
        self.X_train = X_scaled[:split_idx]
        self.X_test = X_scaled[split_idx:]
        self.y_train = y_scaled[:split_idx]
        self.y_test = y_scaled[split_idx:]
        
        print(f"训练集大小: {len(self.X_train)}")
        print(f"测试集大小: {len(self.X_test)}")
        
        # 转换为PyTorch张量
        self.X_train = torch.FloatTensor(self.X_train).unsqueeze(-1)  # [batch, seq_len, 1]
        self.X_test = torch.FloatTensor(self.X_test).unsqueeze(-1)
        self.y_train = torch.FloatTensor(self.y_train)  # [batch, pred_len]
        self.y_test = torch.FloatTensor(self.y_test)
        
    def create_model(self):
        """创建CALF模型"""
        print("正在创建CALF模型...")
        
        # 确保word embedding文件存在
        if not os.path.exists(self.configs.word_embedding_path):
            raise FileNotFoundError(f"Word embedding文件不存在: {self.configs.word_embedding_path}")
        
        self.model = Model(self.configs, self.device)
        self.model.to(self.device)
        
        # 计算模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        print(f"模型总参数数量: {total_params:,}")
        print(f"可训练参数数量: {trainable_params:,}")
        print(f"参数冻结比例: {(total_params - trainable_params) / total_params * 100:.1f}%")
        
    def train_model(self):
        """训练模型"""
        print("开始训练CALF模型...")
        
        # 创建数据加载器
        train_dataset = TensorDataset(self.X_train, self.y_train)
        train_loader = DataLoader(train_dataset, batch_size=self.configs.batch_size, shuffle=True)
        
        # 优化器和损失函数
        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.configs.learning_rate)
        criterion = nn.MSELoss()
        
        # 训练循环
        self.model.train()
        train_losses = []
        
        for epoch in range(self.configs.train_epochs):
            epoch_loss = 0.0
            batch_count = 0
            
            for batch_x, batch_y in train_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                
                # 前向传播
                outputs = self.model(batch_x)
                
                # 计算损失
                loss = criterion(outputs, batch_y)
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                batch_count += 1
            
            avg_loss = epoch_loss / batch_count
            train_losses.append(avg_loss)
            
            if (epoch + 1) % 10 == 0:
                print(f"Epoch [{epoch+1}/{self.configs.train_epochs}], Loss: {avg_loss:.6f}")
        
        print("模型训练完成！")
        return train_losses
        
    def evaluate_model(self):
        """评估模型"""
        print("正在评估模型...")
        
        self.model.eval()
        
        with torch.no_grad():
            # 测试集预测
            X_test_device = self.X_test.to(self.device)
            y_pred_scaled = self.model(X_test_device).cpu().numpy()
            
            # 反标准化
            y_test_original = self.scaler.inverse_transform(
                self.y_test.numpy().reshape(-1, 1)
            ).reshape(self.y_test.shape)
            
            y_pred_original = self.scaler.inverse_transform(
                y_pred_scaled.reshape(-1, 1)
            ).reshape(y_pred_scaled.shape)
        
        # 计算评估指标
        results = {}
        
        # 逐天评估
        for day in range(self.configs.pred_len):
            y_true_day = y_test_original[:, day]
            y_pred_day = y_pred_original[:, day]
            
            mae = mean_absolute_error(y_true_day, y_pred_day)
            mse = mean_squared_error(y_true_day, y_pred_day)
            r2 = r2_score(y_true_day, y_pred_day)
            mape = np.mean(np.abs((y_true_day - y_pred_day) / y_true_day)) * 100
            
            results[f'day_{day+1}'] = {
                'MAE': mae,
                'MSE': mse,
                'R2': r2,
                'MAPE': mape
            }
            
            print(f"第{day+1}天预测 - MAE: {mae:.2f}, R²: {r2:.4f}, MAPE: {mape:.1f}%")
        
        # 整体评估
        y_true_all = y_test_original.flatten()
        y_pred_all = y_pred_original.flatten()
        
        overall_mae = mean_absolute_error(y_true_all, y_pred_all)
        overall_mse = mean_squared_error(y_true_all, y_pred_all)
        overall_r2 = r2_score(y_true_all, y_pred_all)
        overall_mape = np.mean(np.abs((y_true_all - y_pred_all) / y_true_all)) * 100
        
        results['overall'] = {
            'MAE': overall_mae,
            'MSE': overall_mse,
            'R2': overall_r2,
            'MAPE': overall_mape
        }
        
        print(f"\n整体预测 - MAE: {overall_mae:.2f}, R²: {overall_r2:.4f}, MAPE: {overall_mape:.1f}%")
        
        # 保存结果
        self.results = {
            'daily_results': results,
            'predictions': y_pred_original,
            'actuals': y_test_original
        }
        
        return results
        
    def plot_results(self):
        """绘制预测结果"""
        print("正在生成预测结果图表...")
        
        predictions = self.results['predictions']
        actuals = self.results['actuals']
        
        # 创建子图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        # 绘制每天的预测结果
        for day in range(min(self.configs.pred_len, 5)):  # 最多显示5天
            ax = axes[day]
            
            actual = actuals[:, day]
            pred = predictions[:, day]
            
            ax.scatter(actual, pred, alpha=0.6, s=20)
            ax.plot([actual.min(), actual.max()], [actual.min(), actual.max()], 'r--', lw=2)
            ax.set_xlabel('实际径流量')
            ax.set_ylabel('预测径流量')
            ax.set_title(f'第{day+1}天预测 (R²={self.results["daily_results"][f"day_{day+1}"]["R2"]:.3f})')
            ax.grid(True, alpha=0.3)
        
        # 时间序列图
        if len(axes) > self.configs.pred_len:
            ax = axes[self.configs.pred_len]
            
            # 显示最后100个样本的时间序列
            n_samples = min(100, len(actuals))
            x_axis = range(n_samples)
            
            for day in range(min(3, self.configs.pred_len)):  # 显示前3天
                ax.plot(x_axis, actuals[-n_samples:, day], 
                       label=f'实际值-第{day+1}天', alpha=0.7, linestyle='-')
                ax.plot(x_axis, predictions[-n_samples:, day], 
                       label=f'预测值-第{day+1}天', alpha=0.7, linestyle='--')
            
            ax.set_xlabel('样本序号')
            ax.set_ylabel('径流量')
            ax.set_title('时间序列预测对比')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(self.configs.pred_len + 1, len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.show()
        
    def demo_prediction(self):
        """演示预测功能"""
        print(f"\n=== CALF模型{self.configs.pred_len}天径流预测演示 ===")
        
        # 使用最后一个测试样本进行演示
        demo_input = self.X_test[-1:].to(self.device)
        
        self.model.eval()
        with torch.no_grad():
            demo_pred_scaled = self.model(demo_input).cpu().numpy()
            demo_pred = self.scaler.inverse_transform(
                demo_pred_scaled.reshape(-1, 1)
            ).reshape(demo_pred_scaled.shape)
        
        print(f"输入序列长度: {self.configs.seq_len}天")
        print(f"预测序列长度: {self.configs.pred_len}天")
        print(f"\n未来{self.configs.pred_len}天径流预测:")
        
        for i, pred in enumerate(demo_pred[0], 1):
            print(f"  第{i}天: {pred:.2f}")
        
        return demo_pred[0]

def create_configs(seq_len=96, pred_len=7, train_epochs=50):
    """创建CALF模型配置"""
    
    class Configs:
        def __init__(self):
            # 基本配置
            self.task_name = 'long_term_forecast'
            self.seq_len = seq_len
            self.pred_len = pred_len
            self.enc_in = 1  # 输入特征数（只有径流量）
            self.c_out = 1   # 输出特征数
            
            # 模型配置
            self.d_model = 768
            self.n_heads = 12
            self.d_ff = 2048
            self.gpt_layers = 6
            
            # LoRA配置
            self.r = 8
            self.lora_alpha = 32
            self.lora_dropout = 0.1
            
            # 训练配置
            self.train_epochs = train_epochs
            self.batch_size = 32
            self.learning_rate = 0.001
            
            # 文件路径
            self.word_embedding_path = "wte_pca_500.pt"
            
            # 损失权重
            self.task_w = 1.0
            self.feature_w = 0.01
            self.output_w = 1.0
    
    return Configs()

def main():
    """主函数"""
    print("=== CALF径流预测系统 ===")
    
    # 创建配置
    configs = create_configs(seq_len=96, pred_len=7, train_epochs=50)
    
    # 创建预测器
    predictor = CALFRunoffPredictor(configs)
    
    # 执行完整流程
    predictor.load_data()
    predictor.prepare_time_series_data()
    predictor.create_model()
    
    # 训练模型
    train_losses = predictor.train_model()
    
    # 评估模型
    results = predictor.evaluate_model()
    
    # 绘制结果
    predictor.plot_results()
    
    # 演示预测
    demo_pred = predictor.demo_prediction()
    
    print("\n=== 实验完成 ===")
    
    return predictor, results, demo_pred

if __name__ == "__main__":
    predictor, results, demo_pred = main()
