"""
使用粒子群优化算法(PSO)优化CALF模型超参数
目标：减少参数数量，提高NSE和R²性能
7:3数据分割
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

def nash_sutcliffe_efficiency(y_true, y_pred):
    """计算纳什效率系数"""
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    return nse

class OptimizedCALFModel(nn.Module):
    """可配置的CALF模型"""
    
    def __init__(self, seq_len=96, pred_len=7, d_model=256, n_heads=4, n_layers=2):
        super(OptimizedCALFModel, self).__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        
        # 输入投影层
        self.input_projection = nn.Linear(1, d_model)
        
        # 位置编码
        self.pos_embedding = nn.Parameter(torch.randn(seq_len, d_model))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 2,  # 减少FFN维度
            dropout=0.1,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 简化的预测头
        self.prediction_head = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, pred_len)
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)
        
        # 添加位置编码
        x = x + self.pos_embedding.unsqueeze(0).expand(batch_size, -1, -1)
        
        # 层归一化
        x = self.layer_norm(x)
        
        # Transformer编码
        x = self.transformer_encoder(x)
        
        # 全局平均池化
        x = x.mean(dim=1)
        
        # 预测
        output = self.prediction_head(x)
        
        return output

class PSO_CALF_Optimizer:
    """粒子群优化CALF模型"""
    
    def __init__(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 加载和准备数据
        self.prepare_data(data_path)
        
        # PSO参数
        self.n_particles = 10  # 粒子数量
        self.n_iterations = 15  # 迭代次数
        self.w = 0.7  # 惯性权重
        self.c1 = 1.5  # 个体学习因子
        self.c2 = 1.5  # 社会学习因子
        
        # 超参数搜索空间
        self.param_bounds = {
            'd_model': (128, 512),      # 模型维度
            'n_heads': (2, 8),          # 注意力头数
            'n_layers': (1, 4),         # Transformer层数
            'seq_len': (48, 96),        # 输入序列长度
            'learning_rate': (0.0001, 0.002),  # 学习率
            'batch_size': (16, 64)      # 批次大小
        }
        
        # 初始化粒子群
        self.particles = []
        self.velocities = []
        self.personal_best = []
        self.personal_best_scores = []
        self.global_best = None
        self.global_best_score = -float('inf')
        
        self.initialize_particles()
        
    def prepare_data(self, data_path):
        """准备数据"""
        print("正在加载和准备径流数据...")
        
        # 加载数据
        data = pd.read_csv(data_path)
        data['DATE'] = pd.to_datetime(data['DATE'])
        data = data.sort_values('DATE').reset_index(drop=True)
        data = data.dropna()
        
        # 只使用径流数据
        self.runoff_data = data['RUNOFF'].values
        self.scaler = StandardScaler()
        
        print(f"数据加载完成：{len(data)}条记录")
        
    def create_time_series_data(self, seq_len, pred_len=7):
        """创建时间序列数据"""
        X, y = [], []
        
        for i in range(len(self.runoff_data) - seq_len - pred_len + 1):
            X.append(self.runoff_data[i:i + seq_len])
            y.append(self.runoff_data[i + seq_len:i + seq_len + pred_len])
        
        X = np.array(X)
        y = np.array(y)
        
        # 数据标准化
        X_reshaped = X.reshape(-1, 1)
        y_reshaped = y.reshape(-1, 1)
        
        all_data = np.concatenate([X_reshaped, y_reshaped], axis=0)
        self.scaler.fit(all_data)
        
        X_scaled = self.scaler.transform(X_reshaped).reshape(X.shape)
        y_scaled = self.scaler.transform(y_reshaped).reshape(y.shape)
        
        # 按7:3划分数据集
        split_idx = int(len(X) * 0.7)
        
        X_train = torch.FloatTensor(X_scaled[:split_idx]).unsqueeze(-1)
        X_test = torch.FloatTensor(X_scaled[split_idx:]).unsqueeze(-1)
        y_train = torch.FloatTensor(y_scaled[:split_idx])
        y_test = torch.FloatTensor(y_scaled[split_idx:])
        
        return X_train, X_test, y_train, y_test
        
    def initialize_particles(self):
        """初始化粒子群"""
        print("初始化粒子群...")
        
        for i in range(self.n_particles):
            particle = {}
            velocity = {}
            
            for param, (min_val, max_val) in self.param_bounds.items():
                if param in ['n_heads', 'n_layers', 'seq_len', 'batch_size']:
                    # 整数参数
                    particle[param] = np.random.randint(min_val, max_val + 1)
                    velocity[param] = np.random.uniform(-1, 1)
                else:
                    # 浮点数参数
                    particle[param] = np.random.uniform(min_val, max_val)
                    velocity[param] = np.random.uniform(-0.1, 0.1)
            
            # 确保d_model能被n_heads整除
            particle['d_model'] = (particle['d_model'] // particle['n_heads']) * particle['n_heads']
            
            self.particles.append(particle)
            self.velocities.append(velocity)
            self.personal_best.append(particle.copy())
            self.personal_best_scores.append(-float('inf'))
        
        print(f"初始化完成，共{self.n_particles}个粒子")
        
    def evaluate_particle(self, particle):
        """评估粒子性能"""
        try:
            # 创建数据
            X_train, X_test, y_train, y_test = self.create_time_series_data(
                seq_len=int(particle['seq_len'])
            )
            
            # 创建模型
            model = OptimizedCALFModel(
                seq_len=int(particle['seq_len']),
                pred_len=7,
                d_model=int(particle['d_model']),
                n_heads=int(particle['n_heads']),
                n_layers=int(particle['n_layers'])
            ).to(self.device)
            
            # 计算参数数量
            total_params = sum(p.numel() for p in model.parameters())
            
            # 训练模型
            train_dataset = TensorDataset(X_train, y_train)
            train_loader = DataLoader(train_dataset, batch_size=int(particle['batch_size']), shuffle=True)
            
            optimizer = torch.optim.AdamW(model.parameters(), lr=particle['learning_rate'], weight_decay=0.01)
            criterion = nn.MSELoss()
            
            model.train()
            epochs = 20  # 快速训练
            
            for epoch in range(epochs):
                for batch_x, batch_y in train_loader:
                    batch_x = batch_x.to(self.device)
                    batch_y = batch_y.to(self.device)
                    
                    optimizer.zero_grad()
                    outputs = model(batch_x)
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()
            
            # 评估模型
            model.eval()
            with torch.no_grad():
                X_test_device = X_test.to(self.device)
                y_pred_scaled = model(X_test_device).cpu().numpy()
                
                # 反标准化
                y_test_original = self.scaler.inverse_transform(
                    y_test.numpy().reshape(-1, 1)
                ).reshape(y_test.shape)
                
                y_pred_original = self.scaler.inverse_transform(
                    y_pred_scaled.reshape(-1, 1)
                ).reshape(y_pred_scaled.shape)
            
            # 计算整体NSE
            y_true_all = y_test_original.flatten()
            y_pred_all = y_pred_original.flatten()
            nse = nash_sutcliffe_efficiency(y_true_all, y_pred_all)
            
            # 计算适应度：NSE - 参数惩罚
            param_penalty = total_params / 1000000  # 参数数量惩罚（百万为单位）
            fitness = nse - 0.1 * param_penalty  # 平衡性能和复杂度
            
            return fitness, nse, total_params
            
        except Exception as e:
            print(f"评估粒子时出错: {e}")
            return -float('inf'), -float('inf'), float('inf')
    
    def update_particle(self, i):
        """更新粒子位置和速度"""
        particle = self.particles[i]
        velocity = self.velocities[i]
        
        for param in particle.keys():
            # 更新速度
            r1, r2 = np.random.random(), np.random.random()
            velocity[param] = (self.w * velocity[param] + 
                             self.c1 * r1 * (self.personal_best[i][param] - particle[param]) +
                             self.c2 * r2 * (self.global_best[param] - particle[param]))
            
            # 更新位置
            particle[param] += velocity[param]
            
            # 边界约束
            min_val, max_val = self.param_bounds[param]
            particle[param] = np.clip(particle[param], min_val, max_val)
            
            # 整数参数处理
            if param in ['n_heads', 'n_layers', 'seq_len', 'batch_size']:
                particle[param] = int(round(particle[param]))
        
        # 确保d_model能被n_heads整除
        particle['d_model'] = int((particle['d_model'] // particle['n_heads']) * particle['n_heads'])
        
    def optimize(self):
        """执行PSO优化"""
        print("开始粒子群优化...")
        print(f"粒子数量: {self.n_particles}, 迭代次数: {self.n_iterations}")
        
        best_fitness_history = []
        
        for iteration in range(self.n_iterations):
            print(f"\n=== 迭代 {iteration + 1}/{self.n_iterations} ===")
            
            for i in range(self.n_particles):
                print(f"评估粒子 {i + 1}/{self.n_particles}...")
                
                # 评估粒子
                fitness, nse, params = self.evaluate_particle(self.particles[i])
                
                print(f"  粒子{i+1}: NSE={nse:.4f}, 参数={params:,}, 适应度={fitness:.4f}")
                
                # 更新个体最优
                if fitness > self.personal_best_scores[i]:
                    self.personal_best_scores[i] = fitness
                    self.personal_best[i] = self.particles[i].copy()
                
                # 更新全局最优
                if fitness > self.global_best_score:
                    self.global_best_score = fitness
                    self.global_best = self.particles[i].copy()
                    print(f"  *** 发现新的全局最优! 适应度: {fitness:.4f} ***")
            
            best_fitness_history.append(self.global_best_score)
            
            print(f"迭代{iteration + 1}完成，当前最佳适应度: {self.global_best_score:.4f}")
            
            # 更新粒子位置和速度
            if iteration < self.n_iterations - 1:
                for i in range(self.n_particles):
                    self.update_particle(i)
        
        return best_fitness_history
    
    def get_best_model(self):
        """获取最优模型配置"""
        print("\n=== 最优配置 ===")
        print(f"最佳适应度: {self.global_best_score:.4f}")
        print("最优超参数:")
        for param, value in self.global_best.items():
            print(f"  {param}: {value}")
        
        # 计算最优模型的参数数量
        model = OptimizedCALFModel(
            seq_len=int(self.global_best['seq_len']),
            pred_len=7,
            d_model=int(self.global_best['d_model']),
            n_heads=int(self.global_best['n_heads']),
            n_layers=int(self.global_best['n_layers'])
        )
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"最优模型参数数量: {total_params:,}")
        
        return self.global_best, model

def main():
    """主函数"""
    print("=== 粒子群优化CALF模型超参数 ===")
    print("目标: 减少参数数量，提高NSE性能")
    print("数据分割: 7:3")
    
    # 创建优化器
    optimizer = PSO_CALF_Optimizer()
    
    # 执行优化
    fitness_history = optimizer.optimize()
    
    # 获取最优配置
    best_params, best_model = optimizer.get_best_model()
    
    print("\n=== 优化完成 ===")
    print("最优配置已找到，可用于后续训练")
    
    return optimizer, best_params, best_model, fitness_history

if __name__ == "__main__":
    optimizer, best_params, best_model, fitness_history = main()
