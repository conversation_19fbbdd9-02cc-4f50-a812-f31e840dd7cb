{"feature_config": {"lag_days": [1, 2, 3, 4, 5, 6, 7, 14, 21, 30], "ma_windows": [3, 5, 7, 14, 21, 30], "std_windows": [3, 7, 14, 30], "change_lags": [1, 3, 7, 14], "extrema_windows": [3, 7, 14, 30], "use_month": true, "use_day_of_year": true, "use_week_of_year": true, "use_season": true, "use_cyclic_encoding": true}, "model_config": {"n_estimators": 200, "max_depth": 15, "min_samples_split": 3, "min_samples_leaf": 2, "max_features": "sqrt", "bootstrap": true, "random_state": 42, "n_jobs": -1, "model_type": "random_forest"}, "data_config": {"data_path": "1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv", "train_ratio": 0.8, "scale_features": true, "missing_value_strategy": "drop"}, "experiment_config": {"forecast_days": 5, "metrics": ["mae", "mse", "r2", "mape"], "save_model": true, "model_save_path": "./models", "generate_report": true, "report_save_path": "./reports", "plot_results": true, "random_seed": 42}}