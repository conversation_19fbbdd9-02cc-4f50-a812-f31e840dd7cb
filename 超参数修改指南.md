# 超参数修改指南

## 概述

本指南详细说明如何修改超参数配置文件来优化3天和5天径流预测模型的性能。基于实验结果分析，我们提供了具体的优化建议。

## 1. 配置文件结构

### 1.1 配置文件位置
- `config_3day.json` - 3天预测配置
- `config_5day.json` - 5天预测配置
- `config_test.json` - 快速测试配置

### 1.2 配置文件结构
```json
{
  "feature_config": { ... },    // 特征工程配置
  "model_config": { ... },      // 模型参数配置
  "data_config": { ... },       // 数据处理配置
  "experiment_config": { ... }  // 实验设置配置
}
```

## 2. 特征工程配置 (feature_config)

### 2.1 滞后特征配置
```json
"lag_days": [1, 2, 3, 7, 14, 30]
```

**参数说明**：
- 控制使用哪些天数的历史径流数据
- 数值越小，捕捉短期依赖；数值越大，捕捉长期趋势

**优化建议**：
- **3天预测**：`[1, 2, 3, 7, 14]` （减少长期滞后）
- **5天预测**：`[1, 2, 3, 7, 14, 21]` （适度增加中期滞后）
- **快速测试**：`[1, 2, 3, 7]` （最小特征集）

### 2.2 移动平均窗口配置
```json
"ma_windows": [3, 7, 14, 30]
```

**参数说明**：
- 控制移动平均的时间窗口大小
- 用于平滑短期波动，突出趋势

**优化建议**：
- **高性能**：`[3, 7, 14]` （移除30天窗口）
- **平衡性能**：`[3, 7, 14, 21]` （适中窗口）
- **快速测试**：`[3, 7]` （最小窗口）

### 2.3 移动标准差配置
```json
"std_windows": [7, 14, 30]
```

**参数说明**：
- 控制移动标准差的时间窗口
- 用于捕捉径流的波动性特征

**优化建议**：
- **推荐设置**：`[7, 14]` （移除30天窗口）
- **最小设置**：`[7]` （只保留周级波动）

### 2.4 变化率特征配置
```json
"change_lags": [1, 7, 14]
```

**参数说明**：
- 控制计算变化率的时间间隔
- 捕捉径流的变化趋势

**优化建议**：
- **3天预测**：`[1, 7]` （短期变化）
- **5天预测**：`[1, 7, 14]` （中期变化）

### 2.5 时间特征开关
```json
"use_month": true,
"use_day_of_year": true,
"use_week_of_year": false,
"use_season": false,
"use_cyclic_encoding": true
```

**优化建议**：
- 保持 `use_month: true` 和 `use_day_of_year: true`
- 设置 `use_week_of_year: false` 和 `use_season: false`
- 保持 `use_cyclic_encoding: true`

## 3. 模型配置 (model_config)

### 3.1 随机森林参数
```json
"n_estimators": 100,        // 树的数量
"max_depth": 12,           // 最大深度
"min_samples_split": 2,    // 分割所需最小样本数
"min_samples_leaf": 1,     // 叶节点最小样本数
"max_features": "sqrt"     // 每次分割考虑的特征数
```

**优化建议**：

#### 3.1.1 防止过拟合配置
```json
{
  "n_estimators": 80,
  "max_depth": 10,
  "min_samples_split": 5,
  "min_samples_leaf": 3,
  "max_features": "sqrt"
}
```

#### 3.1.2 平衡性能配置
```json
{
  "n_estimators": 100,
  "max_depth": 12,
  "min_samples_split": 3,
  "min_samples_leaf": 2,
  "max_features": "sqrt"
}
```

#### 3.1.3 高精度配置
```json
{
  "n_estimators": 150,
  "max_depth": 15,
  "min_samples_split": 2,
  "min_samples_leaf": 1,
  "max_features": "sqrt"
}
```

### 3.2 模型类型选择
```json
"model_type": "random_forest"  // 可选: "gradient_boosting"
```

**可选值**：
- `"random_forest"` - 随机森林（推荐）
- `"gradient_boosting"` - 梯度提升（可尝试）

## 4. 数据配置 (data_config)

### 4.1 关键参数
```json
"train_ratio": 0.8,                    // 训练集比例
"scale_features": true,                // 是否标准化特征
"missing_value_strategy": "drop"       // 缺失值处理策略
```

**优化建议**：
- 保持 `train_ratio: 0.8`
- 保持 `scale_features: true`
- 保持 `missing_value_strategy: "drop"`

## 5. 实验配置 (experiment_config)

### 5.1 关键参数
```json
"forecast_days": 3,           // 预测天数
"save_model": true,          // 是否保存模型
"generate_report": true,     // 是否生成报告
"plot_results": true         // 是否绘制图表
```

## 6. 优化配置示例

### 6.1 优化后的3天预测配置
```json
{
  "feature_config": {
    "lag_days": [1, 2, 3, 7, 14],
    "ma_windows": [3, 7, 14],
    "std_windows": [7],
    "change_lags": [1, 7],
    "extrema_windows": [7, 14],
    "use_month": true,
    "use_day_of_year": true,
    "use_week_of_year": false,
    "use_season": false,
    "use_cyclic_encoding": true
  },
  "model_config": {
    "n_estimators": 80,
    "max_depth": 10,
    "min_samples_split": 5,
    "min_samples_leaf": 3,
    "max_features": "sqrt",
    "bootstrap": true,
    "random_state": 42,
    "n_jobs": -1,
    "model_type": "random_forest"
  }
}
```

### 6.2 优化后的5天预测配置
```json
{
  "feature_config": {
    "lag_days": [1, 2, 3, 7, 14, 21],
    "ma_windows": [3, 7, 14, 21],
    "std_windows": [7, 14],
    "change_lags": [1, 7, 14],
    "extrema_windows": [7, 14],
    "use_month": true,
    "use_day_of_year": true,
    "use_week_of_year": false,
    "use_season": false,
    "use_cyclic_encoding": true
  },
  "model_config": {
    "n_estimators": 120,
    "max_depth": 12,
    "min_samples_split": 3,
    "min_samples_leaf": 2,
    "max_features": "sqrt",
    "bootstrap": true,
    "random_state": 42,
    "n_jobs": -1,
    "model_type": "random_forest"
  }
}
```

## 7. 修改和测试流程

### 7.1 修改配置文件
1. 打开对应的JSON配置文件
2. 根据优化建议修改参数
3. 保存文件

### 7.2 运行实验
```bash
# 使用配置文件运行实验
python configurable_multistep_predictor.py

# 或者使用修复版预测器
python fixed_multistep_predictor.py
```

### 7.3 结果对比
- 查看控制台输出的R²和MAE指标
- 对比不同配置的性能表现
- 选择最优配置

## 8. 超参数调优策略

### 8.1 网格搜索参数
```python
param_grid = {
    'n_estimators': [50, 80, 100, 120],
    'max_depth': [8, 10, 12, 15],
    'min_samples_split': [2, 3, 5],
    'min_samples_leaf': [1, 2, 3]
}
```

### 8.2 特征选择策略
1. **从少到多**：先用最小特征集，逐步增加
2. **重要性排序**：根据特征重要性选择top特征
3. **交叉验证**：使用交叉验证评估特征组合

### 8.3 性能监控指标
- **R²** > 0.7 为良好
- **MAE** < 500 为可接受
- **MAPE** < 30% 为实用

## 9. 常见问题和解决方案

### 9.1 负R²问题
**原因**：模型过拟合或特征选择不当
**解决**：
- 减少特征数量
- 增加正则化参数
- 降低模型复杂度

### 9.2 训练时间过长
**原因**：特征过多或模型过复杂
**解决**：
- 减少 `n_estimators`
- 降低 `max_depth`
- 减少特征数量

### 9.3 预测精度不稳定
**原因**：随机性或数据分割问题
**解决**：
- 固定 `random_state`
- 使用交叉验证
- 增加训练数据

## 10. 实验建议

### 10.1 快速验证
1. 使用 `config_test.json` 进行快速测试
2. 验证代码运行正常
3. 观察基本性能指标

### 10.2 系统优化
1. 从3天预测开始优化
2. 找到最优配置后应用到5天预测
3. 记录每次实验的配置和结果

### 10.3 性能基准
- 以原始7天实验的第1天结果为基准（R²=0.9601）
- 目标：3天预测R² > 0.8，5天预测R² > 0.6

通过系统性地调整这些超参数，您可以显著提升模型的预测性能。建议从保守的配置开始，逐步优化到最佳性能。
