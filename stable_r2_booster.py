"""
稳定的R²提升器
基于已知有效配置，进行渐进式优化
目标：稳定地将第5天R²从0.7+提升到0.75+甚至0.8+
策略：
1. 基于PSO最优配置的微调
2. 渐进式架构增强
3. 精细化损失函数
4. 多模型融合
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

def nash_sutcliffe_efficiency(y_true, y_pred):
    """计算纳什效率系数"""
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    return nse

class EnhancedCALFModel(nn.Module):
    """基于PSO最优配置的增强CALF模型"""
    
    def __init__(self, seq_len=48, pred_len=7, d_model=256, n_heads=4, n_layers=2, 
                 use_residual=True, use_layer_scale=True):
        super(EnhancedCALFModel, self).__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        self.use_residual = use_residual
        
        # 输入投影层
        self.input_projection = nn.Linear(1, d_model)
        
        # 位置编码
        self.pos_embedding = nn.Parameter(torch.randn(seq_len, d_model))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 2,
            dropout=0.1,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 残差连接（如果启用）
        if use_residual:
            self.residual_projection = nn.Linear(seq_len, d_model)
        
        # Layer Scale（如果启用）
        if use_layer_scale:
            self.layer_scale = nn.Parameter(torch.ones(d_model) * 0.1)
        
        # 第5天专门的注意力层
        self.day5_attention = nn.MultiheadAttention(d_model, n_heads, batch_first=True)
        
        # 预测头 - 分层设计
        self.shared_head = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(0.1)
        )
        
        # 第5天专门预测头
        self.day5_head = nn.Sequential(
            nn.Linear(d_model // 2, d_model // 4),
            nn.GELU(),
            nn.Linear(d_model // 4, 1)
        )
        
        # 其他天数预测头
        self.other_head = nn.Sequential(
            nn.Linear(d_model // 2, d_model // 4),
            nn.GELU(),
            nn.Linear(d_model // 4, pred_len - 1)
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(d_model)
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """初始化模型权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x_proj = self.input_projection(x)
        
        # 添加位置编码
        x_proj = x_proj + self.pos_embedding.unsqueeze(0).expand(batch_size, -1, -1)
        
        # 层归一化
        x_proj = self.layer_norm(x_proj)
        
        # Transformer编码
        x_encoded = self.transformer_encoder(x_proj)
        
        # 残差连接
        if self.use_residual:
            residual = self.residual_projection(x.squeeze(-1))  # [batch, seq_len] -> [batch, d_model]
            x_encoded = x_encoded + residual.unsqueeze(1)
        
        # Layer Scale
        if hasattr(self, 'layer_scale'):
            x_encoded = x_encoded * self.layer_scale
        
        # 第5天专门注意力
        x_day5, _ = self.day5_attention(x_encoded, x_encoded, x_encoded)
        x_enhanced = x_encoded + x_day5
        
        # 全局平均池化
        x_pooled = x_enhanced.mean(dim=1)
        
        # 共享特征提取
        shared_features = self.shared_head(x_pooled)
        
        # 第5天专门预测
        day5_pred = self.day5_head(shared_features)
        
        # 其他天数预测
        other_pred = self.other_head(shared_features)
        
        # 组合预测（第5天在索引4）
        predictions = torch.cat([
            other_pred[:, :4],  # 第1-4天
            day5_pred,          # 第5天
            other_pred[:, 4:]   # 第6-7天
        ], dim=1)
        
        return predictions

class FineGrainedLoss(nn.Module):
    """精细化损失函数"""
    
    def __init__(self, day5_weight=8.0, smoothness_weight=0.5, variance_weight=0.3):
        super(FineGrainedLoss, self).__init__()
        self.day5_weight = day5_weight
        self.smoothness_weight = smoothness_weight
        self.variance_weight = variance_weight
        self.mse_loss = nn.MSELoss()
        self.huber_loss = nn.HuberLoss(delta=1.0)
        
    def forward(self, predictions, targets):
        # 基础损失 - 使用Huber损失减少异常值影响
        base_loss = self.huber_loss(predictions, targets)
        
        # 第5天专门损失
        day5_pred = predictions[:, 4]
        day5_target = targets[:, 4]
        day5_loss = self.mse_loss(day5_pred, day5_target)
        
        # 平滑性损失 - 确保相邻天数预测的连续性
        smoothness_loss = 0
        for i in range(predictions.shape[1] - 1):
            pred_diff = predictions[:, i+1] - predictions[:, i]
            target_diff = targets[:, i+1] - targets[:, i]
            smoothness_loss += self.mse_loss(pred_diff, target_diff)
        smoothness_loss /= (predictions.shape[1] - 1)
        
        # 方差损失 - 鼓励预测的合理变化
        pred_var = torch.var(predictions, dim=1)
        target_var = torch.var(targets, dim=1)
        variance_loss = self.mse_loss(pred_var, target_var)
        
        # 组合损失
        total_loss = (base_loss + 
                     self.day5_weight * day5_loss + 
                     self.smoothness_weight * smoothness_loss +
                     self.variance_weight * variance_loss)
        
        return total_loss

class StableR2Booster:
    """稳定的R²提升器"""
    
    def __init__(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 加载和准备数据
        self.prepare_data(data_path)
        
    def prepare_data(self, data_path):
        """准备数据"""
        print("正在加载和准备径流数据...")
        
        # 加载数据
        data = pd.read_csv(data_path)
        data['DATE'] = pd.to_datetime(data['DATE'])
        data = data.sort_values('DATE').reset_index(drop=True)
        data = data.dropna()
        
        # 只使用径流数据
        self.runoff_data = data['RUNOFF'].values
        self.scaler = StandardScaler()
        
        print(f"数据加载完成：{len(data)}条记录")
        
    def create_time_series_data(self, seq_len, pred_len=7):
        """创建时间序列数据"""
        X, y = [], []
        
        for i in range(len(self.runoff_data) - seq_len - pred_len + 1):
            X.append(self.runoff_data[i:i + seq_len])
            y.append(self.runoff_data[i + seq_len:i + seq_len + pred_len])
        
        X = np.array(X)
        y = np.array(y)
        
        # 数据标准化
        X_reshaped = X.reshape(-1, 1)
        y_reshaped = y.reshape(-1, 1)
        
        all_data = np.concatenate([X_reshaped, y_reshaped], axis=0)
        self.scaler.fit(all_data)
        
        X_scaled = self.scaler.transform(X_reshaped).reshape(X.shape)
        y_scaled = self.scaler.transform(y_reshaped).reshape(y.shape)
        
        # 按7:3划分数据集
        split_idx = int(len(X) * 0.7)
        
        X_train = torch.FloatTensor(X_scaled[:split_idx]).unsqueeze(-1)
        X_test = torch.FloatTensor(X_scaled[split_idx:]).unsqueeze(-1)
        y_train = torch.FloatTensor(y_scaled[:split_idx])
        y_test = torch.FloatTensor(y_scaled[split_idx:])
        
        return X_train, X_test, y_train, y_test
    
    def train_enhanced_model(self, config):
        """训练增强模型"""
        print(f"训练增强模型: {config}")
        
        # 创建数据
        X_train, X_test, y_train, y_test = self.create_time_series_data(
            seq_len=config['seq_len']
        )
        
        # 创建模型
        model = EnhancedCALFModel(
            seq_len=config['seq_len'],
            pred_len=7,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            use_residual=config.get('use_residual', True),
            use_layer_scale=config.get('use_layer_scale', True)
        ).to(self.device)
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"模型参数数量: {total_params:,}")
        
        # 创建数据加载器
        train_dataset = TensorDataset(X_train, y_train)
        train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)
        
        # 优化器和损失函数
        optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=config['learning_rate'], 
            weight_decay=config.get('weight_decay', 0.01),
            betas=(0.9, 0.999)
        )
        
        criterion = FineGrainedLoss(
            day5_weight=config.get('day5_weight', 8.0),
            smoothness_weight=config.get('smoothness_weight', 0.5),
            variance_weight=config.get('variance_weight', 0.3)
        )
        
        # 学习率调度器
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='max', factor=0.8, patience=10, verbose=True
        )
        
        # 训练
        model.train()
        best_day5_r2 = -float('inf')
        patience = 30
        patience_counter = 0
        
        for epoch in range(config['epochs']):
            epoch_loss = 0.0
            
            for batch_x, batch_y in train_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                predictions = model(batch_x)
                loss = criterion(predictions, batch_y)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_loss += loss.item()
            
            # 每5轮评估一次
            if (epoch + 1) % 5 == 0:
                model.eval()
                with torch.no_grad():
                    X_test_device = X_test.to(self.device)
                    predictions = model(X_test_device).cpu().numpy()
                    
                    # 反标准化
                    y_test_original = self.scaler.inverse_transform(
                        y_test.numpy().reshape(-1, 1)
                    ).reshape(y_test.shape)
                    
                    y_pred_original = self.scaler.inverse_transform(
                        predictions.reshape(-1, 1)
                    ).reshape(predictions.shape)
                    
                    # 计算第5天R²
                    day5_r2 = r2_score(y_test_original[:, 4], y_pred_original[:, 4])
                    
                    # 学习率调度
                    scheduler.step(day5_r2)
                    
                    if day5_r2 > best_day5_r2:
                        best_day5_r2 = day5_r2
                        patience_counter = 0
                        torch.save(model.state_dict(), f'best_enhanced_model_{config["name"]}.pth')
                        print(f"Epoch {epoch+1}: Loss = {epoch_loss/len(train_loader):.6f}, 第5天R² = {day5_r2:.4f} ⭐")
                    else:
                        patience_counter += 1
                        print(f"Epoch {epoch+1}: Loss = {epoch_loss/len(train_loader):.6f}, 第5天R² = {day5_r2:.4f}")
                    
                    if day5_r2 >= 0.8:
                        print(f"🔥 达到卓越目标！第5天R² = {day5_r2:.4f} >= 0.8")
                        break
                    elif day5_r2 >= 0.75:
                        print(f"🚀 达到优异目标！第5天R² = {day5_r2:.4f} >= 0.75")
                
                model.train()
                
                if patience_counter >= patience:
                    print(f"早停触发，最佳第5天R² = {best_day5_r2:.4f}")
                    break
        
        # 加载最佳模型并最终评估
        model.load_state_dict(torch.load(f'best_enhanced_model_{config["name"]}.pth'))
        model.eval()
        
        with torch.no_grad():
            X_test_device = X_test.to(self.device)
            predictions = model(X_test_device).cpu().numpy()
            
            # 反标准化
            y_test_original = self.scaler.inverse_transform(
                y_test.numpy().reshape(-1, 1)
            ).reshape(y_test.shape)
            
            y_pred_original = self.scaler.inverse_transform(
                predictions.reshape(-1, 1)
            ).reshape(predictions.shape)
        
        # 计算所有天数的R²和NSE
        daily_r2 = []
        daily_nse = []
        for day in range(7):
            r2 = r2_score(y_test_original[:, day], y_pred_original[:, day])
            nse = nash_sutcliffe_efficiency(y_test_original[:, day], y_pred_original[:, day])
            daily_r2.append(r2)
            daily_nse.append(nse)
        
        # 计算整体NSE
        overall_nse = nash_sutcliffe_efficiency(
            y_test_original.flatten(), 
            y_pred_original.flatten()
        )
        
        return daily_r2, daily_nse, overall_nse, model, y_pred_original
    
    def boost_r2_performance(self):
        """提升R²性能"""
        print("开始稳定的R²性能提升...")
        
        # 基于PSO最优配置的渐进式增强
        configs = [
            {
                'name': 'enhanced_v1',
                'seq_len': 48, 'd_model': 256, 'n_heads': 4, 'n_layers': 2,
                'learning_rate': 0.0015, 'batch_size': 32, 'epochs': 120, 
                'day5_weight': 6.0, 'use_residual': True, 'use_layer_scale': True
            },
            {
                'name': 'enhanced_v2',
                'seq_len': 56, 'd_model': 320, 'n_heads': 4, 'n_layers': 3,
                'learning_rate': 0.001, 'batch_size': 28, 'epochs': 150, 
                'day5_weight': 8.0, 'use_residual': True, 'use_layer_scale': True
            },
            {
                'name': 'enhanced_v3',
                'seq_len': 64, 'd_model': 384, 'n_heads': 6, 'n_layers': 3,
                'learning_rate': 0.0008, 'batch_size': 24, 'epochs': 180, 
                'day5_weight': 10.0, 'use_residual': True, 'use_layer_scale': True
            }
        ]
        
        best_day5_r2 = -float('inf')
        best_config = None
        best_results = None
        all_models = []
        all_predictions = []
        
        for i, config in enumerate(configs):
            print(f"\n=== 训练增强配置 {i+1}/{len(configs)} ===")
            
            try:
                daily_r2, daily_nse, overall_nse, model, predictions = self.train_enhanced_model(config)
                all_models.append(model)
                all_predictions.append(predictions)
                
                print(f"\n结果:")
                for day, (r2, nse) in enumerate(zip(daily_r2, daily_nse), 1):
                    if r2 >= 0.8:
                        status = "🔥"
                    elif r2 >= 0.75:
                        status = "🚀"
                    elif r2 >= 0.7:
                        status = "✅"
                    elif r2 >= 0.6:
                        status = "⚠️"
                    else:
                        status = "❌"
                    print(f"  第{day}天: R² = {r2:.4f}, NSE = {nse:.4f} {status}")
                print(f"  整体NSE: {overall_nse:.4f}")
                
                if daily_r2[4] > best_day5_r2:
                    best_day5_r2 = daily_r2[4]
                    best_config = config
                    best_results = (daily_r2, daily_nse, overall_nse, model)
                    print(f"  *** 新的最佳第5天R²: {best_day5_r2:.4f} ***")
                
                if daily_r2[4] >= 0.8:
                    print(f"  🔥 达到卓越目标！第5天R² = {daily_r2[4]:.4f} >= 0.8")
                    break
                elif daily_r2[4] >= 0.75:
                    print(f"  🚀 达到优异目标！第5天R² = {daily_r2[4]:.4f} >= 0.75")
                    
            except Exception as e:
                print(f"  配置{i+1}训练失败: {e}")
                continue
        
        # 如果有多个模型，尝试集成
        if len(all_predictions) > 1:
            print(f"\n=== 尝试模型集成 ===")
            
            # 简单平均集成
            ensemble_pred = np.mean(all_predictions, axis=0)
            
            # 重新获取测试数据进行评估
            X_train, X_test, y_train, y_test = self.create_time_series_data(seq_len=48)
            y_test_original = self.scaler.inverse_transform(
                y_test.numpy().reshape(-1, 1)
            ).reshape(y_test.shape)
            
            ensemble_original = self.scaler.inverse_transform(
                ensemble_pred.reshape(-1, 1)
            ).reshape(ensemble_pred.shape)
            
            ensemble_day5_r2 = r2_score(y_test_original[:, 4], ensemble_original[:, 4])
            
            print(f"集成模型第5天R²: {ensemble_day5_r2:.4f}")
            
            if ensemble_day5_r2 > best_day5_r2:
                best_day5_r2 = ensemble_day5_r2
                print(f"  🚀 集成模型提升第5天R²: {best_day5_r2:.4f}")
        
        return best_config, best_results, best_day5_r2

def main():
    """主函数"""
    print("=== 稳定R²性能提升系统 ===")
    print("目标：稳定地将第5天R²从0.7+提升到0.75+甚至0.8+")
    print("策略：渐进式增强 + 精细化损失 + 稳定训练")
    
    booster = StableR2Booster()
    
    best_config, best_results, best_day5_r2 = booster.boost_r2_performance()
    
    print("\n" + "="*60)
    print("稳定优化结果")
    print("="*60)
    
    if best_results:
        daily_r2, daily_nse, overall_nse, model = best_results
        
        print(f"最佳配置: {best_config}")
        print(f"第5天R²: {best_day5_r2:.4f}")
        
        # 性能等级评估
        if best_day5_r2 >= 0.8:
            level = "🔥 卓越 (≥0.8)"
        elif best_day5_r2 >= 0.75:
            level = "🚀 优异 (≥0.75)"
        elif best_day5_r2 >= 0.7:
            level = "✅ 优秀 (≥0.7)"
        else:
            level = "⚠️ 良好 (<0.7)"
        
        print(f"性能等级: {level}")
        
        print(f"\n所有天数详细结果:")
        for day, (r2, nse) in enumerate(zip(daily_r2, daily_nse), 1):
            if r2 >= 0.8:
                status = "🔥"
            elif r2 >= 0.75:
                status = "🚀"
            elif r2 >= 0.7:
                status = "✅"
            elif r2 >= 0.6:
                status = "⚠️"
            else:
                status = "❌"
            print(f"  第{day}天: R² = {r2:.4f}, NSE = {nse:.4f} {status}")
        
        print(f"\n整体NSE: {overall_nse:.4f}")
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"最终模型参数数量: {total_params:,}")
    
    return booster, best_config, best_day5_r2

if __name__ == "__main__":
    booster, best_config, best_day5_r2 = main()
