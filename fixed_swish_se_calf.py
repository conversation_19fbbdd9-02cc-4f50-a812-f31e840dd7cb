"""
修复版Swish + SE Layer CALF模型
目标：通过Swish激活函数和SE Layer进一步提升第5天R²
改进：
1. Swish激活函数替代ReLU/GELU
2. SE Layer (Squeeze-and-Excitation)
3. 多尺度注意力机制
4. 第5天专门优化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import pandas as pd
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

def nash_sutcliffe_efficiency(y_true, y_pred):
    """计算纳什效率系数"""
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    return nse

class Swish(nn.Module):
    """Swish激活函数: x * sigmoid(x)"""
    
    def __init__(self):
        super(Swish, self).__init__()
        
    def forward(self, x):
        return x * torch.sigmoid(x)

class SELayer(nn.Module):
    """Squeeze-and-Excitation Layer"""
    
    def __init__(self, channel, reduction=16):
        super(SELayer, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool1d(1)
        self.fc = nn.Sequential(
            nn.Linear(channel, channel // reduction, bias=False),
            Swish(),
            nn.Linear(channel // reduction, channel, bias=False),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # x shape: [batch, seq_len, channel]
        b, s, c = x.size()
        
        # Squeeze: Global Average Pooling
        y = self.avg_pool(x.transpose(1, 2)).view(b, c)  # [batch, channel]
        
        # Excitation: FC layers
        y = self.fc(y).view(b, 1, c)  # [batch, 1, channel]
        
        # Scale
        return x * y.expand_as(x)

class SwishTransformerLayer(nn.Module):
    """使用Swish激活函数的Transformer层"""
    
    def __init__(self, d_model, n_heads, dropout=0.1):
        super(SwishTransformerLayer, self).__init__()
        
        # 多头注意力
        self.self_attn = nn.MultiheadAttention(d_model, n_heads, dropout=dropout, batch_first=True)
        
        # 前馈网络 (使用Swish)
        self.ffn = nn.Sequential(
            nn.Linear(d_model, d_model * 2),
            Swish(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 2, d_model),
            nn.Dropout(dropout)
        )
        
        # 层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # SE Layer
        self.se_layer = SELayer(d_model, reduction=8)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        # 自注意力 + 残差连接
        attn_output, _ = self.self_attn(x, x, x)
        x = self.norm1(x + self.dropout(attn_output))
        
        # SE Layer
        x = self.se_layer(x)
        
        # 前馈网络 + 残差连接
        ffn_output = self.ffn(x)
        x = self.norm2(x + ffn_output)
        
        return x

class FixedSwishSECALFModel(nn.Module):
    """修复版Swish SE CALF模型"""
    
    def __init__(self, seq_len=48, pred_len=7, d_model=256, n_heads=4, n_layers=2):
        super(FixedSwishSECALFModel, self).__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        
        # 输入投影层
        self.input_projection = nn.Linear(1, d_model)
        
        # 位置编码
        self.pos_embedding = nn.Parameter(torch.randn(seq_len, d_model))
        
        # 输入SE Layer
        self.input_se = SELayer(d_model, reduction=8)
        
        # Swish Transformer层
        self.transformer_layers = nn.ModuleList([
            SwishTransformerLayer(d_model, n_heads) 
            for _ in range(n_layers)
        ])
        
        # 第5天专门的注意力层
        self.day5_attention = nn.MultiheadAttention(d_model, n_heads, batch_first=True)
        self.day5_se = SELayer(d_model, reduction=4)
        
        # 预测头 (使用Swish)
        self.prediction_head = nn.Sequential(
            nn.Linear(d_model, d_model),
            Swish(),
            nn.Dropout(0.1),
            SELayer(d_model, reduction=8),
            nn.Linear(d_model, d_model // 2),
            Swish(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, pred_len)
        )
        
        # 第5天增强预测头
        self.day5_enhancer = nn.Sequential(
            nn.Linear(d_model, d_model),
            Swish(),
            nn.Dropout(0.1),
            SELayer(d_model, reduction=4),
            nn.Linear(d_model, d_model // 2),
            Swish(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, d_model // 4),
            Swish(),
            nn.Linear(d_model // 4, 1)
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(d_model)
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """初始化模型权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)
        
        # 添加位置编码
        x = x + self.pos_embedding.unsqueeze(0).expand(batch_size, -1, -1)
        
        # 层归一化
        x = self.layer_norm(x)
        
        # 输入SE Layer
        x = self.input_se(x)
        
        # Swish Transformer编码
        for transformer_layer in self.transformer_layers:
            x = transformer_layer(x)
        
        # 第5天专门的注意力
        x_day5, _ = self.day5_attention(x, x, x)
        x_enhanced = x + x_day5
        x_enhanced = self.day5_se(x_enhanced)
        
        # 全局平均池化
        x_pooled = x_enhanced.mean(dim=1)
        
        # 基础预测
        base_pred = self.prediction_head(x_pooled)
        
        # 第5天增强预测
        day5_enhanced = self.day5_enhancer(x_pooled)
        
        # 替换第5天预测
        final_pred = base_pred.clone()
        final_pred[:, 4] = day5_enhanced.squeeze(-1)
        
        return final_pred

class SwishSELoss(nn.Module):
    """配合Swish SE模型的损失函数"""
    
    def __init__(self, day5_weight=8.0, smoothness_weight=0.3):
        super(SwishSELoss, self).__init__()
        self.day5_weight = day5_weight
        self.smoothness_weight = smoothness_weight
        self.mse_loss = nn.MSELoss()
        self.huber_loss = nn.HuberLoss(delta=1.0)
        
    def forward(self, predictions, targets):
        # 基础Huber损失
        base_loss = self.huber_loss(predictions, targets)
        
        # 第5天专门损失
        day5_pred = predictions[:, 4]
        day5_target = targets[:, 4]
        day5_loss = self.mse_loss(day5_pred, day5_target)
        
        # 平滑性损失
        smoothness_loss = 0
        for i in range(predictions.shape[1] - 1):
            pred_diff = predictions[:, i+1] - predictions[:, i]
            target_diff = targets[:, i+1] - targets[:, i]
            smoothness_loss += self.mse_loss(pred_diff, target_diff)
        smoothness_loss /= (predictions.shape[1] - 1)
        
        # 组合损失
        total_loss = (base_loss + 
                     self.day5_weight * day5_loss + 
                     self.smoothness_weight * smoothness_loss)
        
        return total_loss

class FixedSwishSEOptimizer:
    """修复版Swish SE优化器"""
    
    def __init__(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 加载和准备数据
        self.prepare_data(data_path)
        
    def prepare_data(self, data_path):
        """准备数据"""
        print("正在加载和准备径流数据...")
        
        # 加载数据
        data = pd.read_csv(data_path)
        data['DATE'] = pd.to_datetime(data['DATE'])
        data = data.sort_values('DATE').reset_index(drop=True)
        data = data.dropna()
        
        # 只使用径流数据
        self.runoff_data = data['RUNOFF'].values
        self.scaler = StandardScaler()
        
        print(f"数据加载完成：{len(data)}条记录")
        
    def create_time_series_data(self, seq_len, pred_len=7):
        """创建时间序列数据"""
        X, y = [], []
        
        for i in range(len(self.runoff_data) - seq_len - pred_len + 1):
            X.append(self.runoff_data[i:i + seq_len])
            y.append(self.runoff_data[i + seq_len:i + seq_len + pred_len])
        
        X = np.array(X)
        y = np.array(y)
        
        # 数据标准化
        X_reshaped = X.reshape(-1, 1)
        y_reshaped = y.reshape(-1, 1)
        
        all_data = np.concatenate([X_reshaped, y_reshaped], axis=0)
        self.scaler.fit(all_data)
        
        X_scaled = self.scaler.transform(X_reshaped).reshape(X.shape)
        y_scaled = self.scaler.transform(y_reshaped).reshape(y.shape)
        
        # 按7:3划分数据集
        split_idx = int(len(X) * 0.7)
        
        X_train = torch.FloatTensor(X_scaled[:split_idx]).unsqueeze(-1)
        X_test = torch.FloatTensor(X_scaled[split_idx:]).unsqueeze(-1)
        y_train = torch.FloatTensor(y_scaled[:split_idx])
        y_test = torch.FloatTensor(y_scaled[split_idx:])
        
        return X_train, X_test, y_train, y_test
    
    def train_model(self, config):
        """训练模型"""
        print(f"训练Swish SE模型: {config}")
        
        # 创建数据
        X_train, X_test, y_train, y_test = self.create_time_series_data(
            seq_len=config['seq_len']
        )
        
        # 创建模型
        model = FixedSwishSECALFModel(
            seq_len=config['seq_len'],
            pred_len=7,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers']
        ).to(self.device)
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"Swish SE模型参数数量: {total_params:,}")
        
        # 创建数据加载器
        train_dataset = TensorDataset(X_train, y_train)
        train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)
        
        # 优化器和损失函数
        optimizer = torch.optim.AdamW(
            model.parameters(), 
            lr=config['learning_rate'], 
            weight_decay=0.01
        )
        
        criterion = SwishSELoss(
            day5_weight=config.get('day5_weight', 8.0),
            smoothness_weight=config.get('smoothness_weight', 0.3)
        )
        
        # 学习率调度器
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer, T_max=config['epochs'], eta_min=1e-6
        )
        
        # 训练
        model.train()
        best_day5_r2 = -float('inf')
        patience = 20
        patience_counter = 0
        
        for epoch in range(config['epochs']):
            epoch_loss = 0.0
            
            for batch_x, batch_y in train_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                predictions = model(batch_x)
                loss = criterion(predictions, batch_y)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_loss += loss.item()
            
            scheduler.step()
            
            # 每5轮评估一次
            if (epoch + 1) % 5 == 0:
                model.eval()
                with torch.no_grad():
                    X_test_device = X_test.to(self.device)
                    predictions = model(X_test_device).cpu().numpy()
                    
                    # 反标准化
                    y_test_original = self.scaler.inverse_transform(
                        y_test.numpy().reshape(-1, 1)
                    ).reshape(y_test.shape)
                    
                    y_pred_original = self.scaler.inverse_transform(
                        predictions.reshape(-1, 1)
                    ).reshape(predictions.shape)
                    
                    # 计算第5天R²
                    day5_r2 = r2_score(y_test_original[:, 4], y_pred_original[:, 4])
                    
                    if day5_r2 > best_day5_r2:
                        best_day5_r2 = day5_r2
                        patience_counter = 0
                        torch.save(model.state_dict(), f'best_fixed_swish_se_{config["name"]}.pth')
                        print(f"Epoch {epoch+1}: Loss = {epoch_loss/len(train_loader):.6f}, 第5天R² = {day5_r2:.4f} ⭐")
                    else:
                        patience_counter += 1
                        print(f"Epoch {epoch+1}: Loss = {epoch_loss/len(train_loader):.6f}, 第5天R² = {day5_r2:.4f}")
                    
                    if day5_r2 >= 0.8:
                        print(f"🔥 达到卓越目标！第5天R² = {day5_r2:.4f} >= 0.8")
                        break
                    elif day5_r2 >= 0.75:
                        print(f"🚀 达到优异目标！第5天R² = {day5_r2:.4f} >= 0.75")
                    elif day5_r2 >= 0.7:
                        print(f"✅ 达到优秀目标！第5天R² = {day5_r2:.4f} >= 0.7")
                
                model.train()
                
                if patience_counter >= patience:
                    print(f"早停触发，最佳第5天R² = {best_day5_r2:.4f}")
                    break
        
        # 加载最佳模型并最终评估
        model.load_state_dict(torch.load(f'best_fixed_swish_se_{config["name"]}.pth'))
        model.eval()
        
        with torch.no_grad():
            X_test_device = X_test.to(self.device)
            predictions = model(X_test_device).cpu().numpy()
            
            # 反标准化
            y_test_original = self.scaler.inverse_transform(
                y_test.numpy().reshape(-1, 1)
            ).reshape(y_test.shape)
            
            y_pred_original = self.scaler.inverse_transform(
                predictions.reshape(-1, 1)
            ).reshape(predictions.shape)
        
        # 计算所有天数的R²和NSE
        daily_r2 = []
        daily_nse = []
        for day in range(7):
            r2 = r2_score(y_test_original[:, day], y_pred_original[:, day])
            nse = nash_sutcliffe_efficiency(y_test_original[:, day], y_pred_original[:, day])
            daily_r2.append(r2)
            daily_nse.append(nse)
        
        # 计算整体NSE
        overall_nse = nash_sutcliffe_efficiency(
            y_test_original.flatten(), 
            y_pred_original.flatten()
        )
        
        return daily_r2, daily_nse, overall_nse, model
    
    def optimize_models(self):
        """优化模型"""
        print("开始Swish + SE Layer优化...")
        
        # 配置
        configs = [
            {
                'name': 'v1',
                'seq_len': 48, 'd_model': 256, 'n_heads': 4, 'n_layers': 2,
                'learning_rate': 0.0015, 'batch_size': 32, 'epochs': 100, 
                'day5_weight': 6.0, 'smoothness_weight': 0.3
            },
            {
                'name': 'v2',
                'seq_len': 56, 'd_model': 320, 'n_heads': 4, 'n_layers': 3,
                'learning_rate': 0.001, 'batch_size': 28, 'epochs': 120, 
                'day5_weight': 8.0, 'smoothness_weight': 0.4
            }
        ]
        
        best_day5_r2 = -float('inf')
        best_config = None
        best_results = None
        
        for i, config in enumerate(configs):
            print(f"\n=== 训练配置 {i+1}/{len(configs)} ===")
            
            try:
                daily_r2, daily_nse, overall_nse, model = self.train_model(config)
                
                print(f"\n结果:")
                for day, (r2, nse) in enumerate(zip(daily_r2, daily_nse), 1):
                    if r2 >= 0.8:
                        status = "🔥"
                    elif r2 >= 0.75:
                        status = "🚀"
                    elif r2 >= 0.7:
                        status = "✅"
                    elif r2 >= 0.6:
                        status = "⚠️"
                    else:
                        status = "❌"
                    print(f"  第{day}天: R² = {r2:.4f}, NSE = {nse:.4f} {status}")
                print(f"  整体NSE: {overall_nse:.4f}")
                
                if daily_r2[4] > best_day5_r2:
                    best_day5_r2 = daily_r2[4]
                    best_config = config
                    best_results = (daily_r2, daily_nse, overall_nse, model)
                    print(f"  *** 新的最佳第5天R²: {best_day5_r2:.4f} ***")
                
                if daily_r2[4] >= 0.75:
                    print(f"  🚀 达到优异目标！第5天R² = {daily_r2[4]:.4f} >= 0.75")
                    break
                    
            except Exception as e:
                print(f"  配置{i+1}训练失败: {e}")
                continue
        
        return best_config, best_results, best_day5_r2

def main():
    """主函数"""
    print("=== 修复版 Swish + SE Layer CALF模型 ===")
    print("改进点:")
    print("1. 🔥 Swish激活函数 (x * sigmoid(x))")
    print("2. 🎯 SE Layer注意力机制")
    print("3. 🚀 自定义Swish Transformer层")
    print("4. ✅ 第5天专门SE优化")
    
    optimizer = FixedSwishSEOptimizer()
    
    best_config, best_results, best_day5_r2 = optimizer.optimize_models()
    
    print("\n" + "="*60)
    print("Swish + SE Layer 优化结果")
    print("="*60)
    
    if best_results:
        daily_r2, daily_nse, overall_nse, model = best_results
        
        print(f"最佳配置: {best_config}")
        print(f"第5天R²: {best_day5_r2:.4f}")
        
        # 性能等级评估
        if best_day5_r2 >= 0.8:
            level = "🔥 卓越 (≥0.8)"
        elif best_day5_r2 >= 0.75:
            level = "🚀 优异 (≥0.75)"
        elif best_day5_r2 >= 0.7:
            level = "✅ 优秀 (≥0.7)"
        else:
            level = "⚠️ 良好 (<0.7)"
        
        print(f"性能等级: {level}")
        
        print(f"\n所有天数详细结果:")
        for day, (r2, nse) in enumerate(zip(daily_r2, daily_nse), 1):
            if r2 >= 0.8:
                status = "🔥"
            elif r2 >= 0.75:
                status = "🚀"
            elif r2 >= 0.7:
                status = "✅"
            elif r2 >= 0.6:
                status = "⚠️"
            else:
                status = "❌"
            print(f"  第{day}天: R² = {r2:.4f}, NSE = {nse:.4f} {status}")
        
        print(f"\n整体NSE: {overall_nse:.4f}")
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"Swish SE模型参数数量: {total_params:,}")
        
    else:
        print("未找到有效配置")
        print(f"最佳第5天R²: {best_day5_r2:.4f}")
    
    return optimizer, best_config, best_day5_r2

if __name__ == "__main__":
    optimizer, best_config, best_day5_r2 = main()
