"""
使用粒子群优化后的最优参数训练CALF模型
基于PSO优化结果：NSE=0.7035，参数=143,395
7:3数据分割，主要评价指标：NSE和R²
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def nash_sutcliffe_efficiency(y_true, y_pred):
    """计算纳什效率系数"""
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    return nse

class OptimizedCALFModel(nn.Module):
    """PSO优化后的CALF模型"""
    
    def __init__(self, seq_len=48, pred_len=7, d_model=256, n_heads=4, n_layers=2):
        super(OptimizedCALFModel, self).__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        
        # 输入投影层
        self.input_projection = nn.Linear(1, d_model)
        
        # 位置编码
        self.pos_embedding = nn.Parameter(torch.randn(seq_len, d_model))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 2,
            dropout=0.1,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 预测头
        self.prediction_head = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, pred_len)
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(d_model)
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """初始化模型权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)
        
        # 添加位置编码
        x = x + self.pos_embedding.unsqueeze(0).expand(batch_size, -1, -1)
        
        # 层归一化
        x = self.layer_norm(x)
        
        # Transformer编码
        x = self.transformer_encoder(x)
        
        # 全局平均池化
        x = x.mean(dim=1)
        
        # 预测
        output = self.prediction_head(x)
        
        return output

class OptimizedCALFPredictor:
    """PSO优化后的CALF径流预测器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # PSO优化得到的最优参数
        self.optimal_params = {
            'seq_len': 48,
            'pred_len': 7,
            'd_model': 256,
            'n_heads': 4,
            'n_layers': 2,
            'learning_rate': 0.0015,
            'batch_size': 32
        }
        
        print("PSO优化最优参数:")
        for param, value in self.optimal_params.items():
            print(f"  {param}: {value}")
        
        self.data = None
        self.model = OptimizedCALFModel(
            seq_len=self.optimal_params['seq_len'],
            pred_len=self.optimal_params['pred_len'],
            d_model=self.optimal_params['d_model'],
            n_heads=self.optimal_params['n_heads'],
            n_layers=self.optimal_params['n_layers']
        )
        self.scaler = StandardScaler()
        self.results = {}
        
    def load_and_prepare_data(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        """加载和准备数据"""
        print("正在加载和准备径流数据...")
        
        # 加载数据
        self.data = pd.read_csv(data_path)
        self.data['DATE'] = pd.to_datetime(self.data['DATE'])
        self.data = self.data.sort_values('DATE').reset_index(drop=True)
        self.data = self.data.dropna()
        
        # 只使用径流数据
        runoff_data = self.data['RUNOFF'].values
        
        # 创建时间序列样本
        seq_len = self.optimal_params['seq_len']
        pred_len = self.optimal_params['pred_len']
        
        X, y = [], []
        
        for i in range(len(runoff_data) - seq_len - pred_len + 1):
            X.append(runoff_data[i:i + seq_len])
            y.append(runoff_data[i + seq_len:i + seq_len + pred_len])
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"生成样本数量: {len(X)}")
        print(f"输入序列长度: {seq_len}, 预测序列长度: {pred_len}")
        
        # 数据标准化
        X_reshaped = X.reshape(-1, 1)
        y_reshaped = y.reshape(-1, 1)
        
        all_data = np.concatenate([X_reshaped, y_reshaped], axis=0)
        self.scaler.fit(all_data)
        
        X_scaled = self.scaler.transform(X_reshaped).reshape(X.shape)
        y_scaled = self.scaler.transform(y_reshaped).reshape(y.shape)
        
        # 按7:3划分数据集
        split_idx = int(len(X) * 0.7)
        
        self.X_train = torch.FloatTensor(X_scaled[:split_idx]).unsqueeze(-1)
        self.X_test = torch.FloatTensor(X_scaled[split_idx:]).unsqueeze(-1)
        self.y_train = torch.FloatTensor(y_scaled[:split_idx])
        self.y_test = torch.FloatTensor(y_scaled[split_idx:])
        
        print(f"训练集大小: {len(self.X_train)} (70%)")
        print(f"测试集大小: {len(self.X_test)} (30%)")
        
    def train_model(self, epochs=100):
        """训练模型"""
        print("开始训练PSO优化后的CALF模型...")
        
        self.model.to(self.device)
        
        # 创建数据加载器
        train_dataset = TensorDataset(self.X_train, self.y_train)
        train_loader = DataLoader(
            train_dataset, 
            batch_size=self.optimal_params['batch_size'], 
            shuffle=True
        )
        
        # 优化器和损失函数
        optimizer = torch.optim.AdamW(
            self.model.parameters(), 
            lr=self.optimal_params['learning_rate'], 
            weight_decay=0.01
        )
        criterion = nn.MSELoss()
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs, eta_min=1e-6)
        
        # 计算模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        print(f"模型参数数量: {total_params:,} (PSO优化目标: ~143,395)")
        
        # 训练循环
        self.model.train()
        best_loss = float('inf')
        patience = 20
        patience_counter = 0
        
        for epoch in range(epochs):
            epoch_loss = 0.0
            batch_count = 0
            
            for batch_x, batch_y in train_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_x)
                loss = criterion(outputs, batch_y)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                epoch_loss += loss.item()
                batch_count += 1
            
            scheduler.step()
            
            avg_loss = epoch_loss / batch_count
            
            # 早停机制
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
                torch.save(self.model.state_dict(), 'best_optimized_calf_model.pth')
            else:
                patience_counter += 1
            
            if (epoch + 1) % 10 == 0:
                print(f"Epoch [{epoch+1}/{epochs}], Loss: {avg_loss:.6f}, LR: {scheduler.get_last_lr()[0]:.6f}")
            
            # 早停
            if patience_counter >= patience:
                print(f"早停触发，在第{epoch+1}轮停止训练")
                break
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_optimized_calf_model.pth'))
        print("模型训练完成！")
        
    def evaluate_model(self):
        """评估模型"""
        print("正在评估PSO优化后的模型...")
        
        self.model.eval()
        
        with torch.no_grad():
            # 测试集预测
            X_test_device = self.X_test.to(self.device)
            y_pred_scaled = self.model(X_test_device).cpu().numpy()
            
            # 反标准化
            y_test_original = self.scaler.inverse_transform(
                self.y_test.numpy().reshape(-1, 1)
            ).reshape(self.y_test.shape)
            
            y_pred_original = self.scaler.inverse_transform(
                y_pred_scaled.reshape(-1, 1)
            ).reshape(y_pred_scaled.shape)
        
        # 计算评估指标
        results = {}
        
        print(f"\n=== PSO优化CALF模型{self.optimal_params['pred_len']}天径流预测结果 ===")
        print("主要评价指标: NSE (纳什效率系数) 和 R² (决定系数)")
        print("模型参数: PSO优化后的最优配置")
        print("-" * 70)
        
        # 逐天评估
        for day in range(self.optimal_params['pred_len']):
            y_true_day = y_test_original[:, day]
            y_pred_day = y_pred_original[:, day]
            
            mae = mean_absolute_error(y_true_day, y_pred_day)
            mse = mean_squared_error(y_true_day, y_pred_day)
            r2 = r2_score(y_true_day, y_pred_day)
            nse = nash_sutcliffe_efficiency(y_true_day, y_pred_day)
            mape = np.mean(np.abs((y_true_day - y_pred_day) / y_true_day)) * 100
            
            results[f'day_{day+1}'] = {
                'MAE': mae,
                'MSE': mse,
                'R2': r2,
                'NSE': nse,
                'MAPE': mape
            }
            
            # NSE评价等级
            if nse > 0.75:
                nse_grade = "优秀"
            elif nse > 0.65:
                nse_grade = "很好"
            elif nse > 0.50:
                nse_grade = "满意"
            elif nse > 0.20:
                nse_grade = "不满意"
            else:
                nse_grade = "不可接受"
            
            print(f"第{day+1}天预测 - R²: {r2:.4f}, NSE: {nse:.4f} ({nse_grade}), MAE: {mae:.2f}, MAPE: {mape:.1f}%")
        
        print("-" * 70)
        
        # 整体评估
        y_true_all = y_test_original.flatten()
        y_pred_all = y_pred_original.flatten()
        
        overall_mae = mean_absolute_error(y_true_all, y_pred_all)
        overall_mse = mean_squared_error(y_true_all, y_pred_all)
        overall_r2 = r2_score(y_true_all, y_pred_all)
        overall_nse = nash_sutcliffe_efficiency(y_true_all, y_pred_all)
        overall_mape = np.mean(np.abs((y_true_all - y_pred_all) / y_true_all)) * 100
        
        # 整体NSE评价等级
        if overall_nse > 0.75:
            overall_nse_grade = "优秀"
        elif overall_nse > 0.65:
            overall_nse_grade = "很好"
        elif overall_nse > 0.50:
            overall_nse_grade = "满意"
        elif overall_nse > 0.20:
            overall_nse_grade = "不满意"
        else:
            overall_nse_grade = "不可接受"
        
        results['overall'] = {
            'MAE': overall_mae,
            'MSE': overall_mse,
            'R2': overall_r2,
            'NSE': overall_nse,
            'MAPE': overall_mape,
            'NSE_grade': overall_nse_grade
        }
        
        print(f"整体预测 - R²: {overall_r2:.4f}, NSE: {overall_nse:.4f} ({overall_nse_grade}), MAE: {overall_mae:.2f}, MAPE: {overall_mape:.1f}%")
        
        # 保存结果
        self.results = {
            'daily_results': results,
            'predictions': y_pred_original,
            'actuals': y_test_original
        }
        
        return results
        
    def demo_prediction(self):
        """演示预测功能"""
        print(f"\n=== PSO优化CALF模型{self.optimal_params['pred_len']}天径流预测演示 ===")
        
        # 使用最后一个测试样本进行演示
        demo_input = self.X_test[-1:].to(self.device)
        
        self.model.eval()
        with torch.no_grad():
            demo_pred_scaled = self.model(demo_input).cpu().numpy()
            demo_pred = self.scaler.inverse_transform(
                demo_pred_scaled.reshape(-1, 1)
            ).reshape(demo_pred_scaled.shape)
        
        print(f"输入序列长度: {self.optimal_params['seq_len']}天")
        print(f"预测序列长度: {self.optimal_params['pred_len']}天")
        print(f"\n未来{self.optimal_params['pred_len']}天径流预测:")
        
        for i, pred in enumerate(demo_pred[0], 1):
            print(f"  第{i}天: {pred:.2f}")
        
        return demo_pred[0]

def main():
    """主函数"""
    print("=== PSO优化后的CALF径流预测系统 ===")
    print("模型配置: 粒子群优化后的最优参数")
    print("目标: 减少参数数量，提高NSE性能")
    print("评价指标: NSE (纳什效率系数) 和 R² (决定系数)")
    print("数据分割: 70%训练集, 30%测试集")
    
    # 创建预测器
    predictor = OptimizedCALFPredictor()
    
    # 执行完整流程
    predictor.load_and_prepare_data()
    predictor.train_model(epochs=100)
    results = predictor.evaluate_model()
    demo_pred = predictor.demo_prediction()
    
    print("\n=== 实验完成 ===")
    print("模型架构: PSO优化CALF (参数精简版)")
    print("数据分割: 7:3")
    print("预测任务: 7天径流预测")
    print("主要指标: NSE和R²")
    print("优化状态: 粒子群优化完成")
    
    return predictor, results, demo_pred

if __name__ == "__main__":
    predictor, results, demo_pred = main()
