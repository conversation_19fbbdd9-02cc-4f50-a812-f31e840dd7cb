"""
运行径流预测模型的简化脚本
"""

from runoff_prediction_config import RunoffPredictor
import matplotlib.pyplot as plt

def main():
    print("长北地区径流预测模型")
    print("="*40)
    
    # 方案选择
    print("请选择预测方案:")
    print("1. 使用气象特征预测径流")
    print("2. 使用历史径流数据预测未来径流")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == '1':
        method = 'weather_features'
        print("\n使用气象特征预测径流...")
    elif choice == '2':
        method = 'time_series'
        print("\n使用历史径流数据预测未来径流...")
    else:
        print("无效选择，默认使用方案1")
        method = 'weather_features'
    
    # 创建预测器
    predictor = RunoffPredictor()
    
    try:
        # 加载和预处理数据
        predictor.load_and_preprocess_data(method=method)
        
        # 训练模型
        predictor.train_model(model_type='random_forest')
        
        # 评估模型
        results = predictor.evaluate_model()
        
        # 绘制结果
        predictor.plot_results(results)
        
        print("\n模型训练和评估完成！")
        print("图表已显示，请查看预测效果。")
        
    except Exception as e:
        print(f"运行出错: {e}")
        print("请检查数据文件是否存在且格式正确。")

if __name__ == "__main__":
    main()
