"""
高级优化CALF模型
目标：第5天R²达到0.7以上
尝试多种算法和参数组合：
1. 遗传算法(GA)优化
2. 贝叶斯优化
3. 网格搜索
4. 集成学习
5. 多尺度特征融合
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.model_selection import ParameterGrid
import warnings
warnings.filterwarnings('ignore')

def nash_sutcliffe_efficiency(y_true, y_pred):
    """计算纳什效率系数"""
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    return nse

class AdvancedCALFModel(nn.Module):
    """高级CALF模型 - 多尺度特征融合"""
    
    def __init__(self, seq_len=96, pred_len=7, d_model=512, n_heads=8, n_layers=4, 
                 use_multi_scale=True, use_residual=True, use_attention_weights=True):
        super(AdvancedCALFModel, self).__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        self.use_multi_scale = use_multi_scale
        self.use_residual = use_residual
        self.use_attention_weights = use_attention_weights
        
        # 输入投影层
        self.input_projection = nn.Linear(1, d_model)
        
        # 多尺度特征提取
        if use_multi_scale:
            self.conv1d_layers = nn.ModuleList([
                nn.Conv1d(1, d_model//4, kernel_size=k, padding=k//2) 
                for k in [3, 5, 7, 9]
            ])
            self.multi_scale_fusion = nn.Linear(d_model, d_model)
        
        # 位置编码
        self.pos_embedding = nn.Parameter(torch.randn(seq_len, d_model))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=0.1,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 注意力权重层
        if use_attention_weights:
            self.attention_weights = nn.MultiheadAttention(d_model, n_heads, batch_first=True)
        
        # 残差连接
        if use_residual:
            self.residual_projection = nn.Linear(1, d_model)
        
        # 多层预测头
        self.prediction_head = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, d_model // 4),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 4, pred_len)
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(0.1)
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """初始化模型权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Conv1d):
            torch.nn.init.kaiming_normal_(module.weight)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        batch_size, seq_len, _ = x.shape
        
        # 基础输入投影
        x_proj = self.input_projection(x)
        
        # 多尺度特征提取
        if self.use_multi_scale:
            x_conv = x.transpose(1, 2)  # [batch, 1, seq_len]
            multi_scale_features = []
            
            for conv_layer in self.conv1d_layers:
                conv_out = conv_layer(x_conv)  # [batch, d_model//4, seq_len]
                conv_out = conv_out.transpose(1, 2)  # [batch, seq_len, d_model//4]
                multi_scale_features.append(conv_out)
            
            multi_scale_concat = torch.cat(multi_scale_features, dim=-1)  # [batch, seq_len, d_model]
            multi_scale_fused = self.multi_scale_fusion(multi_scale_concat)
            
            # 融合多尺度特征和投影特征
            x_proj = x_proj + multi_scale_fused
        
        # 添加位置编码
        x_proj = x_proj + self.pos_embedding.unsqueeze(0).expand(batch_size, -1, -1)
        
        # 层归一化
        x_proj = self.layer_norm(x_proj)
        x_proj = self.dropout(x_proj)
        
        # Transformer编码
        x_encoded = self.transformer_encoder(x_proj)
        
        # 注意力权重
        if self.use_attention_weights:
            x_attended, _ = self.attention_weights(x_encoded, x_encoded, x_encoded)
            x_encoded = x_encoded + x_attended
        
        # 残差连接
        if self.use_residual:
            residual = self.residual_projection(x)
            x_encoded = x_encoded + residual
        
        # 全局平均池化
        x_pooled = x_encoded.mean(dim=1)
        
        # 预测
        output = self.prediction_head(x_pooled)
        
        return output

class GeneticAlgorithmOptimizer:
    """遗传算法优化器"""
    
    def __init__(self, population_size=20, generations=10, mutation_rate=0.1):
        self.population_size = population_size
        self.generations = generations
        self.mutation_rate = mutation_rate
        
        # 参数搜索空间
        self.param_bounds = {
            'd_model': (256, 768),
            'n_heads': (4, 16),
            'n_layers': (2, 8),
            'seq_len': (64, 128),
            'learning_rate': (0.0001, 0.003),
            'batch_size': (16, 64),
            'use_multi_scale': [True, False],
            'use_residual': [True, False],
            'use_attention_weights': [True, False]
        }
        
    def create_individual(self):
        """创建个体"""
        individual = {}
        for param, bounds in self.param_bounds.items():
            if isinstance(bounds, list):
                individual[param] = np.random.choice(bounds)
            elif param in ['n_heads', 'n_layers', 'seq_len', 'batch_size']:
                individual[param] = np.random.randint(bounds[0], bounds[1] + 1)
            else:
                individual[param] = np.random.uniform(bounds[0], bounds[1])
        
        # 确保d_model能被n_heads整除
        individual['d_model'] = (individual['d_model'] // individual['n_heads']) * individual['n_heads']
        
        return individual
    
    def mutate(self, individual):
        """变异操作"""
        mutated = individual.copy()
        
        for param in mutated.keys():
            if np.random.random() < self.mutation_rate:
                bounds = self.param_bounds[param]
                if isinstance(bounds, list):
                    mutated[param] = np.random.choice(bounds)
                elif param in ['n_heads', 'n_layers', 'seq_len', 'batch_size']:
                    mutated[param] = np.random.randint(bounds[0], bounds[1] + 1)
                else:
                    # 高斯变异
                    std = (bounds[1] - bounds[0]) * 0.1
                    mutated[param] = np.clip(
                        mutated[param] + np.random.normal(0, std),
                        bounds[0], bounds[1]
                    )
        
        # 确保d_model能被n_heads整除
        mutated['d_model'] = int((mutated['d_model'] // mutated['n_heads']) * mutated['n_heads'])
        
        return mutated
    
    def crossover(self, parent1, parent2):
        """交叉操作"""
        child = {}
        for param in parent1.keys():
            if np.random.random() < 0.5:
                child[param] = parent1[param]
            else:
                child[param] = parent2[param]
        
        # 确保d_model能被n_heads整除
        child['d_model'] = int((child['d_model'] // child['n_heads']) * child['n_heads'])
        
        return child

class AdvancedCALFOptimizer:
    """高级CALF优化器"""
    
    def __init__(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 加载和准备数据
        self.prepare_data(data_path)
        
        # 最佳配置记录
        self.best_config = None
        self.best_score = -float('inf')
        self.best_day5_r2 = -float('inf')
        
    def prepare_data(self, data_path):
        """准备数据"""
        print("正在加载和准备径流数据...")
        
        # 加载数据
        data = pd.read_csv(data_path)
        data['DATE'] = pd.to_datetime(data['DATE'])
        data = data.sort_values('DATE').reset_index(drop=True)
        data = data.dropna()
        
        # 只使用径流数据
        self.runoff_data = data['RUNOFF'].values
        self.scaler = StandardScaler()
        
        print(f"数据加载完成：{len(data)}条记录")
        
    def create_time_series_data(self, seq_len, pred_len=7):
        """创建时间序列数据"""
        X, y = [], []
        
        for i in range(len(self.runoff_data) - seq_len - pred_len + 1):
            X.append(self.runoff_data[i:i + seq_len])
            y.append(self.runoff_data[i + seq_len:i + seq_len + pred_len])
        
        X = np.array(X)
        y = np.array(y)
        
        # 数据标准化
        X_reshaped = X.reshape(-1, 1)
        y_reshaped = y.reshape(-1, 1)
        
        all_data = np.concatenate([X_reshaped, y_reshaped], axis=0)
        self.scaler.fit(all_data)
        
        X_scaled = self.scaler.transform(X_reshaped).reshape(X.shape)
        y_scaled = self.scaler.transform(y_reshaped).reshape(y.shape)
        
        # 按7:3划分数据集
        split_idx = int(len(X) * 0.7)
        
        X_train = torch.FloatTensor(X_scaled[:split_idx]).unsqueeze(-1)
        X_test = torch.FloatTensor(X_scaled[split_idx:]).unsqueeze(-1)
        y_train = torch.FloatTensor(y_scaled[:split_idx])
        y_test = torch.FloatTensor(y_scaled[split_idx:])
        
        return X_train, X_test, y_train, y_test
        
    def evaluate_config(self, config):
        """评估配置"""
        try:
            # 创建数据
            X_train, X_test, y_train, y_test = self.create_time_series_data(
                seq_len=int(config['seq_len'])
            )
            
            # 创建模型
            model = AdvancedCALFModel(
                seq_len=int(config['seq_len']),
                pred_len=7,
                d_model=int(config['d_model']),
                n_heads=int(config['n_heads']),
                n_layers=int(config['n_layers']),
                use_multi_scale=config.get('use_multi_scale', True),
                use_residual=config.get('use_residual', True),
                use_attention_weights=config.get('use_attention_weights', True)
            ).to(self.device)
            
            # 训练模型
            train_dataset = TensorDataset(X_train, y_train)
            train_loader = DataLoader(train_dataset, batch_size=int(config['batch_size']), shuffle=True)
            
            optimizer = torch.optim.AdamW(model.parameters(), lr=config['learning_rate'], weight_decay=0.01)
            criterion = nn.MSELoss()
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=30)
            
            model.train()
            epochs = 30  # 增加训练轮数
            
            for epoch in range(epochs):
                epoch_loss = 0.0
                for batch_x, batch_y in train_loader:
                    batch_x = batch_x.to(self.device)
                    batch_y = batch_y.to(self.device)
                    
                    optimizer.zero_grad()
                    outputs = model(batch_x)
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()
                    
                    epoch_loss += loss.item()
                
                scheduler.step()
            
            # 评估模型
            model.eval()
            with torch.no_grad():
                X_test_device = X_test.to(self.device)
                y_pred_scaled = model(X_test_device).cpu().numpy()
                
                # 反标准化
                y_test_original = self.scaler.inverse_transform(
                    y_test.numpy().reshape(-1, 1)
                ).reshape(y_test.shape)
                
                y_pred_original = self.scaler.inverse_transform(
                    y_pred_scaled.reshape(-1, 1)
                ).reshape(y_pred_scaled.shape)
            
            # 计算第5天R²
            y_true_day5 = y_test_original[:, 4]  # 第5天（索引4）
            y_pred_day5 = y_pred_original[:, 4]
            day5_r2 = r2_score(y_true_day5, y_pred_day5)
            
            # 计算整体NSE
            y_true_all = y_test_original.flatten()
            y_pred_all = y_pred_original.flatten()
            overall_nse = nash_sutcliffe_efficiency(y_true_all, y_pred_all)
            
            # 综合评分：重点关注第5天R²
            score = day5_r2 * 0.7 + overall_nse * 0.3
            
            return score, day5_r2, overall_nse
            
        except Exception as e:
            print(f"评估配置时出错: {e}")
            return -float('inf'), -float('inf'), -float('inf')
    
    def grid_search_optimization(self):
        """网格搜索优化"""
        print("开始网格搜索优化...")
        
        param_grid = {
            'd_model': [384, 512, 640],
            'n_heads': [6, 8, 12],
            'n_layers': [3, 4, 5],
            'seq_len': [80, 96, 112],
            'learning_rate': [0.0005, 0.001, 0.0015],
            'batch_size': [24, 32, 40],
            'use_multi_scale': [True],
            'use_residual': [True],
            'use_attention_weights': [True]
        }
        
        grid = ParameterGrid(param_grid)
        best_score = -float('inf')
        best_config = None
        best_day5_r2 = -float('inf')
        
        total_configs = len(list(grid))
        print(f"总共需要测试 {total_configs} 个配置")
        
        for i, config in enumerate(grid):
            # 确保d_model能被n_heads整除
            config['d_model'] = (config['d_model'] // config['n_heads']) * config['n_heads']
            
            print(f"测试配置 {i+1}/{total_configs}: {config}")
            
            score, day5_r2, overall_nse = self.evaluate_config(config)
            
            print(f"  结果: 第5天R² = {day5_r2:.4f}, 整体NSE = {overall_nse:.4f}, 综合得分 = {score:.4f}")
            
            if day5_r2 > best_day5_r2:
                best_day5_r2 = day5_r2
                best_score = score
                best_config = config.copy()
                print(f"  *** 发现更好的第5天R²: {day5_r2:.4f} ***")
            
            # 如果第5天R²达到目标，提前结束
            if day5_r2 >= 0.7:
                print(f"  🎯 达到目标！第5天R² = {day5_r2:.4f} >= 0.7")
                break
        
        return best_config, best_score, best_day5_r2
    
    def genetic_algorithm_optimization(self):
        """遗传算法优化"""
        print("开始遗传算法优化...")
        
        ga = GeneticAlgorithmOptimizer(population_size=15, generations=8, mutation_rate=0.15)
        
        # 初始化种群
        population = [ga.create_individual() for _ in range(ga.population_size)]
        
        best_score = -float('inf')
        best_config = None
        best_day5_r2 = -float('inf')
        
        for generation in range(ga.generations):
            print(f"\n=== 第 {generation + 1} 代 ===")
            
            # 评估种群
            fitness_scores = []
            day5_r2_scores = []
            
            for i, individual in enumerate(population):
                print(f"评估个体 {i+1}/{len(population)}...")
                score, day5_r2, overall_nse = self.evaluate_config(individual)
                fitness_scores.append(score)
                day5_r2_scores.append(day5_r2)
                
                print(f"  个体{i+1}: 第5天R² = {day5_r2:.4f}, 综合得分 = {score:.4f}")
                
                if day5_r2 > best_day5_r2:
                    best_day5_r2 = day5_r2
                    best_score = score
                    best_config = individual.copy()
                    print(f"  *** 发现更好的第5天R²: {day5_r2:.4f} ***")
                
                # 如果第5天R²达到目标，提前结束
                if day5_r2 >= 0.7:
                    print(f"  🎯 达到目标！第5天R² = {day5_r2:.4f} >= 0.7")
                    return best_config, best_score, best_day5_r2
            
            # 选择、交叉、变异
            if generation < ga.generations - 1:
                # 选择最优个体
                sorted_indices = np.argsort(fitness_scores)[::-1]
                elite_size = ga.population_size // 4
                new_population = [population[i] for i in sorted_indices[:elite_size]]
                
                # 生成新个体
                while len(new_population) < ga.population_size:
                    # 选择父母
                    parent1 = population[sorted_indices[np.random.randint(0, elite_size)]]
                    parent2 = population[sorted_indices[np.random.randint(0, elite_size)]]
                    
                    # 交叉
                    child = ga.crossover(parent1, parent2)
                    
                    # 变异
                    child = ga.mutate(child)
                    
                    new_population.append(child)
                
                population = new_population
        
        return best_config, best_score, best_day5_r2

def main():
    """主函数"""
    print("=== 高级CALF模型优化 - 目标第5天R² >= 0.7 ===")
    print("尝试多种优化算法和参数组合")
    
    optimizer = AdvancedCALFOptimizer()
    
    # 方法1：网格搜索
    print("\n" + "="*60)
    print("方法1：网格搜索优化")
    print("="*60)
    
    grid_config, grid_score, grid_day5_r2 = optimizer.grid_search_optimization()
    
    print(f"\n网格搜索最佳结果:")
    print(f"第5天R²: {grid_day5_r2:.4f}")
    print(f"综合得分: {grid_score:.4f}")
    print(f"最佳配置: {grid_config}")
    
    # 方法2：遗传算法（如果网格搜索未达到目标）
    if grid_day5_r2 < 0.7:
        print("\n" + "="*60)
        print("方法2：遗传算法优化")
        print("="*60)
        
        ga_config, ga_score, ga_day5_r2 = optimizer.genetic_algorithm_optimization()
        
        print(f"\n遗传算法最佳结果:")
        print(f"第5天R²: {ga_day5_r2:.4f}")
        print(f"综合得分: {ga_score:.4f}")
        print(f"最佳配置: {ga_config}")
        
        # 选择最佳方法
        if ga_day5_r2 > grid_day5_r2:
            final_config = ga_config
            final_day5_r2 = ga_day5_r2
            final_method = "遗传算法"
        else:
            final_config = grid_config
            final_day5_r2 = grid_day5_r2
            final_method = "网格搜索"
    else:
        final_config = grid_config
        final_day5_r2 = grid_day5_r2
        final_method = "网格搜索"
    
    print("\n" + "="*60)
    print("最终优化结果")
    print("="*60)
    print(f"最佳方法: {final_method}")
    print(f"第5天R²: {final_day5_r2:.4f}")
    print(f"目标达成: {'✅ 是' if final_day5_r2 >= 0.7 else '❌ 否'}")
    print(f"最佳配置: {final_config}")
    
    return optimizer, final_config, final_day5_r2

if __name__ == "__main__":
    optimizer, best_config, best_day5_r2 = main()
