"""
超参数配置文件
方便修改和调试不同的超参数组合
"""

import json
from dataclasses import dataclass, asdict
from typing import List, Dict, Any

@dataclass
class FeatureConfig:
    """特征工程配置"""
    # 滞后特征配置
    lag_days: List[int] = None
    
    # 移动平均窗口配置
    ma_windows: List[int] = None
    
    # 移动标准差窗口配置
    std_windows: List[int] = None
    
    # 变化率特征配置
    change_lags: List[int] = None
    
    # 极值特征配置
    extrema_windows: List[int] = None
    
    # 时间特征开关
    use_month: bool = True
    use_day_of_year: bool = True
    use_week_of_year: bool = False
    use_season: bool = False
    
    # 周期性编码开关
    use_cyclic_encoding: bool = True
    
    def __post_init__(self):
        """设置默认值"""
        if self.lag_days is None:
            self.lag_days = [1, 2, 3, 7, 14, 21, 30]
        if self.ma_windows is None:
            self.ma_windows = [3, 7, 14, 30]
        if self.std_windows is None:
            self.std_windows = [7, 14, 30]
        if self.change_lags is None:
            self.change_lags = [1, 7, 14]
        if self.extrema_windows is None:
            self.extrema_windows = [7, 14, 30]

@dataclass
class ModelConfig:
    """模型配置"""
    # 随机森林参数
    n_estimators: int = 100
    max_depth: int = 12
    min_samples_split: int = 2
    min_samples_leaf: int = 1
    max_features: str = 'sqrt'  # 'sqrt', 'log2', None, int, float
    bootstrap: bool = True
    random_state: int = 42
    n_jobs: int = -1
    
    # 其他模型选项
    model_type: str = 'random_forest'  # 'random_forest', 'gradient_boosting', 'xgboost'

@dataclass
class DataConfig:
    """数据配置"""
    # 数据文件路径
    data_path: str = '1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'
    
    # 数据分割比例
    train_ratio: float = 0.8
    
    # 是否标准化特征
    scale_features: bool = True
    
    # 缺失值处理方式
    missing_value_strategy: str = 'drop'  # 'drop', 'interpolate', 'forward_fill'

@dataclass
class ExperimentConfig:
    """实验配置"""
    # 预测天数
    forecast_days: int = 3
    
    # 评估指标
    metrics: List[str] = None
    
    # 是否保存模型
    save_model: bool = True
    
    # 模型保存路径
    model_save_path: str = './models'
    
    # 是否生成报告
    generate_report: bool = True
    
    # 报告保存路径
    report_save_path: str = './reports'
    
    # 是否绘制图表
    plot_results: bool = True
    
    # 随机种子
    random_seed: int = 42
    
    def __post_init__(self):
        """设置默认值"""
        if self.metrics is None:
            self.metrics = ['mae', 'mse', 'r2', 'mape']

@dataclass
class HyperparameterConfig:
    """完整的超参数配置"""
    feature_config: FeatureConfig = None
    model_config: ModelConfig = None
    data_config: DataConfig = None
    experiment_config: ExperimentConfig = None
    
    def __post_init__(self):
        """初始化子配置"""
        if self.feature_config is None:
            self.feature_config = FeatureConfig()
        if self.model_config is None:
            self.model_config = ModelConfig()
        if self.data_config is None:
            self.data_config = DataConfig()
        if self.experiment_config is None:
            self.experiment_config = ExperimentConfig()
    
    def save_to_file(self, filepath: str):
        """保存配置到文件"""
        config_dict = asdict(self)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load_from_file(cls, filepath: str):
        """从文件加载配置"""
        with open(filepath, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        return cls(
            feature_config=FeatureConfig(**config_dict['feature_config']),
            model_config=ModelConfig(**config_dict['model_config']),
            data_config=DataConfig(**config_dict['data_config']),
            experiment_config=ExperimentConfig(**config_dict['experiment_config'])
        )

# 预定义配置方案
class ConfigPresets:
    """预定义的配置方案"""
    
    @staticmethod
    def get_quick_test_config():
        """快速测试配置（较少特征，较小模型）"""
        return HyperparameterConfig(
            feature_config=FeatureConfig(
                lag_days=[1, 2, 3, 7],
                ma_windows=[3, 7],
                std_windows=[7],
                change_lags=[1, 7],
                extrema_windows=[7],
                use_cyclic_encoding=False
            ),
            model_config=ModelConfig(
                n_estimators=50,
                max_depth=8,
                random_state=42
            ),
            experiment_config=ExperimentConfig(
                forecast_days=3,
                plot_results=False
            )
        )
    
    @staticmethod
    def get_high_accuracy_config():
        """高精度配置（更多特征，更大模型）"""
        return HyperparameterConfig(
            feature_config=FeatureConfig(
                lag_days=[1, 2, 3, 4, 5, 6, 7, 14, 21, 30],
                ma_windows=[3, 5, 7, 14, 21, 30],
                std_windows=[3, 7, 14, 30],
                change_lags=[1, 3, 7, 14],
                extrema_windows=[3, 7, 14, 30],
                use_week_of_year=True,
                use_season=True,
                use_cyclic_encoding=True
            ),
            model_config=ModelConfig(
                n_estimators=200,
                max_depth=15,
                min_samples_split=3,
                min_samples_leaf=2,
                random_state=42
            ),
            experiment_config=ExperimentConfig(
                forecast_days=5,
                save_model=True,
                generate_report=True
            )
        )
    
    @staticmethod
    def get_balanced_config():
        """平衡配置（中等复杂度）"""
        return HyperparameterConfig(
            feature_config=FeatureConfig(
                lag_days=[1, 2, 3, 7, 14, 30],
                ma_windows=[3, 7, 14, 30],
                std_windows=[7, 14],
                change_lags=[1, 7],
                extrema_windows=[7, 14],
                use_cyclic_encoding=True
            ),
            model_config=ModelConfig(
                n_estimators=100,
                max_depth=12,
                random_state=42
            ),
            experiment_config=ExperimentConfig(
                forecast_days=3,
                generate_report=True
            )
        )

# 使用示例和配置生成
def create_default_configs():
    """创建默认配置文件"""
    
    # 3天预测配置
    config_3day = ConfigPresets.get_balanced_config()
    config_3day.experiment_config.forecast_days = 3
    config_3day.save_to_file('config_3day.json')
    
    # 5天预测配置
    config_5day = ConfigPresets.get_high_accuracy_config()
    config_5day.experiment_config.forecast_days = 5
    config_5day.save_to_file('config_5day.json')
    
    # 快速测试配置
    config_test = ConfigPresets.get_quick_test_config()
    config_test.save_to_file('config_test.json')
    
    print("默认配置文件已创建:")
    print("- config_3day.json: 3天预测配置")
    print("- config_5day.json: 5天预测配置") 
    print("- config_test.json: 快速测试配置")

def print_config_summary(config: HyperparameterConfig):
    """打印配置摘要"""
    print("=== 配置摘要 ===")
    print(f"预测天数: {config.experiment_config.forecast_days}")
    print(f"滞后特征: {config.feature_config.lag_days}")
    print(f"移动平均窗口: {config.feature_config.ma_windows}")
    print(f"模型类型: {config.model_config.model_type}")
    print(f"树的数量: {config.model_config.n_estimators}")
    print(f"最大深度: {config.model_config.max_depth}")
    print(f"训练比例: {config.data_config.train_ratio}")
    print(f"特征标准化: {config.data_config.scale_features}")
    print("=" * 20)

if __name__ == "__main__":
    # 创建默认配置文件
    create_default_configs()
    
    # 演示配置使用
    print("\n=== 配置使用演示 ===")
    
    # 加载3天预测配置
    config_3day = HyperparameterConfig.load_from_file('config_3day.json')
    print("\n3天预测配置:")
    print_config_summary(config_3day)
    
    # 加载5天预测配置
    config_5day = HyperparameterConfig.load_from_file('config_5day.json')
    print("\n5天预测配置:")
    print_config_summary(config_5day)
