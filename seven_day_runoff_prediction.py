"""
7天径流预测模型
支持递归预测和直接多步预测两种方案
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SevenDayRunoffPredictor:
    """7天径流预测器"""
    
    def __init__(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.data_path = data_path
        self.data = None
        self.models = {}  # 存储不同预测方法的模型
        self.scalers = {}  # 存储不同方法的标准化器
        
    def load_and_prepare_data(self):
        """加载和预处理数据"""
        print("正在加载数据...")
        self.data = pd.read_csv(self.data_path)
        self.data['DATE'] = pd.to_datetime(self.data['DATE'])
        self.data = self.data.sort_values('DATE').reset_index(drop=True)
        
        # 处理缺失值
        self.data = self.data.dropna()
        
        print(f"数据加载完成，共{len(self.data)}条记录")
        
    def create_features_for_recursive(self):
        """为递归预测创建特征（与1天预测相同）"""
        print("创建递归预测特征...")
        
        # 创建滞后特征
        lag_days = [1, 2, 3, 7, 14, 30]
        for lag in lag_days:
            self.data[f'RUNOFF_LAG_{lag}'] = self.data['RUNOFF'].shift(lag)
        
        # 创建移动平均特征
        windows = [3, 7, 14, 30]
        for window in windows:
            self.data[f'RUNOFF_MA_{window}'] = self.data['RUNOFF'].rolling(window=window).mean()
        
        # 时间特征
        self.data['MONTH'] = self.data['DATE'].dt.month
        self.data['DAY_OF_YEAR'] = self.data['DATE'].dt.dayofyear
        
        # 删除缺失值
        self.data = self.data.dropna()
        
        feature_columns = [f'RUNOFF_LAG_{lag}' for lag in lag_days] + \
                         [f'RUNOFF_MA_{window}' for window in windows] + \
                         ['MONTH', 'DAY_OF_YEAR']
        
        return feature_columns
    
    def create_features_for_direct(self, forecast_days=7):
        """为直接多步预测创建特征"""
        print(f"创建{forecast_days}天直接预测特征...")
        
        # 创建更多滞后特征（需要更长的历史）
        lag_days = [1, 2, 3, 4, 5, 6, 7, 14, 21, 30]
        for lag in lag_days:
            self.data[f'RUNOFF_LAG_{lag}'] = self.data['RUNOFF'].shift(lag)
        
        # 创建多个移动平均特征
        windows = [3, 7, 14, 21, 30]
        for window in windows:
            self.data[f'RUNOFF_MA_{window}'] = self.data['RUNOFF'].rolling(window=window).mean()
            self.data[f'RUNOFF_STD_{window}'] = self.data['RUNOFF'].rolling(window=window).std()
        
        # 创建变化率特征
        for lag in [1, 7, 14]:
            self.data[f'RUNOFF_CHANGE_{lag}'] = self.data['RUNOFF'] - self.data[f'RUNOFF_LAG_{lag}']
        
        # 时间特征
        self.data['MONTH'] = self.data['DATE'].dt.month
        self.data['DAY_OF_YEAR'] = self.data['DATE'].dt.dayofyear
        self.data['WEEK_OF_YEAR'] = self.data['DATE'].dt.isocalendar().week
        
        # 周期性特征
        self.data['MONTH_SIN'] = np.sin(2 * np.pi * self.data['MONTH'] / 12)
        self.data['MONTH_COS'] = np.cos(2 * np.pi * self.data['MONTH'] / 12)
        
        # 删除缺失值
        self.data = self.data.dropna()
        
        # 创建多步预测目标
        for day in range(1, forecast_days + 1):
            self.data[f'RUNOFF_TARGET_{day}'] = self.data['RUNOFF'].shift(-day)
        
        # 删除最后几行（没有目标值）
        self.data = self.data.iloc[:-forecast_days]
        
        feature_columns = [f'RUNOFF_LAG_{lag}' for lag in lag_days] + \
                         [f'RUNOFF_MA_{window}' for window in windows] + \
                         [f'RUNOFF_STD_{window}' for window in windows] + \
                         [f'RUNOFF_CHANGE_{lag}' for lag in [1, 7, 14]] + \
                         ['MONTH', 'DAY_OF_YEAR', 'WEEK_OF_YEAR', 'MONTH_SIN', 'MONTH_COS']
        
        target_columns = [f'RUNOFF_TARGET_{day}' for day in range(1, forecast_days + 1)]
        
        return feature_columns, target_columns
    
    def train_recursive_model(self):
        """训练递归预测模型（1天模型，用于递归预测7天）"""
        print("训练递归预测模型...")
        
        feature_columns = self.create_features_for_recursive()
        
        X = self.data[feature_columns]
        y = self.data['RUNOFF']
        
        # 时间序列分割
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        # 标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 训练模型
        model = RandomForestRegressor(n_estimators=100, max_depth=12, random_state=42)
        model.fit(X_train_scaled, y_train)
        
        # 保存模型和标准化器
        self.models['recursive'] = model
        self.scalers['recursive'] = scaler
        
        # 保存特征列名和数据
        self.recursive_features = feature_columns
        self.recursive_train_data = (X_train, y_train)
        self.recursive_test_data = (X_test, y_test)
        
        print("递归预测模型训练完成！")
        
    def train_direct_model(self, forecast_days=7):
        """训练直接多步预测模型"""
        print(f"训练{forecast_days}天直接预测模型...")
        
        feature_columns, target_columns = self.create_features_for_direct(forecast_days)
        
        X = self.data[feature_columns]
        y = self.data[target_columns]
        
        # 时间序列分割
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        # 标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 为每一天训练一个模型
        models = {}
        for i, day in enumerate(range(1, forecast_days + 1)):
            print(f"  训练第{day}天预测模型...")
            model = RandomForestRegressor(n_estimators=100, max_depth=12, random_state=42+i)
            model.fit(X_train_scaled, y_train.iloc[:, i])
            models[f'day_{day}'] = model
        
        # 保存模型和标准化器
        self.models['direct'] = models
        self.scalers['direct'] = scaler
        
        # 保存特征列名和数据
        self.direct_features = feature_columns
        self.direct_train_data = (X_train, y_train)
        self.direct_test_data = (X_test, y_test)
        
        print("直接预测模型训练完成！")
        
    def predict_recursive(self, initial_data, forecast_days=7):
        """递归预测方法"""
        model = self.models['recursive']
        scaler = self.scalers['recursive']
        
        predictions = []
        current_data = initial_data.copy()
        
        for day in range(forecast_days):
            # 准备当前输入特征
            X_current = current_data[self.recursive_features].iloc[-1:].values
            X_current_scaled = scaler.transform(X_current)
            
            # 预测下一天
            pred = model.predict(X_current_scaled)[0]
            predictions.append(pred)
            
            # 更新数据：添加预测值作为新的观测值
            new_row = current_data.iloc[-1:].copy()
            new_row['RUNOFF'] = pred
            new_row['DATE'] = new_row['DATE'] + pd.Timedelta(days=1)
            
            # 重新计算特征
            new_row = self._update_features_for_recursive(new_row, current_data)
            current_data = pd.concat([current_data, new_row], ignore_index=True)
        
        return np.array(predictions)
    
    def _update_features_for_recursive(self, new_row, historical_data):
        """为递归预测更新特征"""
        # 合并历史数据和新行
        temp_data = pd.concat([historical_data, new_row], ignore_index=True)
        
        # 重新计算滞后特征
        lag_days = [1, 2, 3, 7, 14, 30]
        for lag in lag_days:
            if len(temp_data) > lag:
                new_row[f'RUNOFF_LAG_{lag}'] = temp_data['RUNOFF'].iloc[-lag-1]
        
        # 重新计算移动平均特征
        windows = [3, 7, 14, 30]
        for window in windows:
            if len(temp_data) >= window:
                new_row[f'RUNOFF_MA_{window}'] = temp_data['RUNOFF'].iloc[-window:].mean()
        
        # 更新时间特征
        new_row['MONTH'] = new_row['DATE'].dt.month
        new_row['DAY_OF_YEAR'] = new_row['DATE'].dt.dayofyear
        
        return new_row
    
    def predict_direct(self, X_input):
        """直接多步预测方法"""
        models = self.models['direct']
        scaler = self.scalers['direct']
        
        X_scaled = scaler.transform(X_input)
        predictions = []
        
        for day in range(1, 8):  # 预测7天
            model = models[f'day_{day}']
            pred = model.predict(X_scaled)
            predictions.append(pred)
        
        return np.array(predictions).T  # 转置以匹配样本数 x 预测天数的格式
    
    def evaluate_models(self):
        """评估两种预测方法"""
        print("\n=== 模型评估 ===")
        
        # 评估递归预测
        print("\n1. 递归预测评估:")
        self._evaluate_recursive()
        
        # 评估直接预测
        print("\n2. 直接预测评估:")
        self._evaluate_direct()
    
    def _evaluate_recursive(self):
        """评估递归预测"""
        X_test, y_test = self.recursive_test_data
        
        # 选择测试集的前100个样本进行评估（递归预测计算量大）
        n_samples = min(100, len(X_test))
        
        all_predictions = []
        all_actuals = []
        
        for i in range(n_samples):
            # 获取当前样本之前的历史数据
            historical_end_idx = int(len(self.data) * 0.8) + i
            historical_data = self.data.iloc[:historical_end_idx].copy()
            
            # 递归预测7天
            predictions = self.predict_recursive(historical_data, forecast_days=7)
            
            # 获取实际值
            actual_start_idx = historical_end_idx
            actual_end_idx = min(actual_start_idx + 7, len(self.data))
            actuals = self.data['RUNOFF'].iloc[actual_start_idx:actual_end_idx].values
            
            # 确保预测和实际值长度一致
            min_len = min(len(predictions), len(actuals))
            if min_len > 0:
                all_predictions.extend(predictions[:min_len])
                all_actuals.extend(actuals[:min_len])
        
        if len(all_predictions) > 0:
            mae = mean_absolute_error(all_actuals, all_predictions)
            mse = mean_squared_error(all_actuals, all_predictions)
            r2 = r2_score(all_actuals, all_predictions)
            
            print(f"  递归预测 - MAE: {mae:.2f}, MSE: {mse:.2f}, R²: {r2:.4f}")
        else:
            print("  递归预测评估失败：无有效预测数据")
    
    def _evaluate_direct(self):
        """评估直接预测"""
        X_test, y_test = self.direct_test_data
        
        # 直接预测
        predictions = self.predict_direct(X_test.values)
        
        # 计算每天的评估指标
        for day in range(7):
            actual = y_test.iloc[:, day].values
            pred = predictions[:, day]
            
            mae = mean_absolute_error(actual, pred)
            mse = mean_squared_error(actual, pred)
            r2 = r2_score(actual, pred)
            
            print(f"  第{day+1}天预测 - MAE: {mae:.2f}, MSE: {mse:.2f}, R²: {r2:.4f}")
        
        # 整体评估
        all_actual = y_test.values.flatten()
        all_pred = predictions.flatten()
        
        overall_mae = mean_absolute_error(all_actual, all_pred)
        overall_mse = mean_squared_error(all_actual, all_pred)
        overall_r2 = r2_score(all_actual, all_pred)
        
        print(f"  整体预测 - MAE: {overall_mae:.2f}, MSE: {overall_mse:.2f}, R²: {overall_r2:.4f}")
    
    def plot_comparison(self):
        """绘制预测结果比较"""
        print("\n正在生成预测结果图表...")
        
        # 获取测试数据
        X_test, y_test = self.direct_test_data
        
        # 直接预测
        direct_predictions = self.predict_direct(X_test.values[:50])  # 只取前50个样本
        
        # 绘制结果
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        axes = axes.flatten()
        
        for day in range(7):
            ax = axes[day]
            
            actual = y_test.iloc[:50, day].values
            direct_pred = direct_predictions[:, day]
            
            ax.scatter(actual, direct_pred, alpha=0.6, label='直接预测')
            ax.plot([actual.min(), actual.max()], [actual.min(), actual.max()], 'r--', lw=2)
            ax.set_xlabel('实际径流量')
            ax.set_ylabel('预测径流量')
            ax.set_title(f'第{day+1}天预测')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # 最后一个子图显示整体比较
        ax = axes[7]
        all_actual = y_test.iloc[:50].values.flatten()
        all_direct = direct_predictions.flatten()
        
        ax.scatter(all_actual, all_direct, alpha=0.4, label='直接预测（所有天数）')
        ax.plot([all_actual.min(), all_actual.max()], [all_actual.min(), all_actual.max()], 'r--', lw=2)
        ax.set_xlabel('实际径流量')
        ax.set_ylabel('预测径流量')
        ax.set_title('7天预测整体效果')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

# 使用示例
if __name__ == "__main__":
    # 创建7天预测器
    predictor = SevenDayRunoffPredictor()
    
    # 加载数据
    predictor.load_and_prepare_data()
    
    # 训练递归预测模型
    predictor.train_recursive_model()
    
    # 训练直接预测模型
    predictor.train_direct_model(forecast_days=7)
    
    # 评估模型
    predictor.evaluate_models()
    
    # 绘制比较图
    predictor.plot_comparison()
    
    print("\n7天径流预测模型训练和评估完成！")
