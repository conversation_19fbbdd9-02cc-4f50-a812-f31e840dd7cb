"""
简化版CALF径流预测器
避免复杂的GPT2集成，专注于径流预测任务
使用7:3数据分割
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
import os
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SimplifiedCALFModel(nn.Module):
    """简化版CALF模型，专注于径流预测"""
    
    def __init__(self, seq_len=96, pred_len=7, d_model=768, n_heads=12, n_layers=6):
        super(SimplifiedCALFModel, self).__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        
        # 输入投影层
        self.input_projection = nn.Linear(1, d_model)
        
        # 位置编码
        self.pos_embedding = nn.Parameter(torch.randn(seq_len, d_model))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=0.1,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, pred_len)
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        # x shape: [batch_size, seq_len, 1]
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)  # [batch_size, seq_len, d_model]
        
        # 添加位置编码
        x = x + self.pos_embedding.unsqueeze(0).expand(batch_size, -1, -1)
        
        # 层归一化
        x = self.layer_norm(x)
        
        # Transformer编码
        x = self.transformer_encoder(x)  # [batch_size, seq_len, d_model]
        
        # 全局平均池化
        x = x.mean(dim=1)  # [batch_size, d_model]
        
        # 输出投影
        output = self.output_projection(x)  # [batch_size, pred_len]
        
        return output

class SimplifiedCALFPredictor:
    """简化版CALF径流预测器"""
    
    def __init__(self, seq_len=96, pred_len=7, d_model=512, n_heads=8, n_layers=4):
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        self.data = None
        self.model = SimplifiedCALFModel(seq_len, pred_len, d_model, n_heads, n_layers)
        self.scaler = StandardScaler()
        self.results = {}
        
    def load_data(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        """加载径流数据"""
        print("正在加载径流数据...")
        self.data = pd.read_csv(data_path)
        self.data['DATE'] = pd.to_datetime(self.data['DATE'])
        self.data = self.data.sort_values('DATE').reset_index(drop=True)
        self.data = self.data.dropna()
        print(f"数据加载完成：{len(self.data)}条记录")
        
    def prepare_time_series_data(self):
        """准备时间序列数据"""
        print("正在准备时间序列数据...")
        
        # 只使用径流数据进行时间序列预测
        runoff_data = self.data['RUNOFF'].values
        
        # 创建时间序列样本
        X, y = [], []
        
        for i in range(len(runoff_data) - self.seq_len - self.pred_len + 1):
            # 输入序列：过去seq_len天的径流数据
            X.append(runoff_data[i:i + self.seq_len])
            # 输出序列：未来pred_len天的径流数据
            y.append(runoff_data[i + self.seq_len:i + self.seq_len + self.pred_len])
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"生成样本数量: {len(X)}")
        print(f"输入序列长度: {self.seq_len}, 预测序列长度: {self.pred_len}")
        
        # 数据标准化
        X_reshaped = X.reshape(-1, 1)
        y_reshaped = y.reshape(-1, 1)
        
        # 拟合标准化器
        all_data = np.concatenate([X_reshaped, y_reshaped], axis=0)
        self.scaler.fit(all_data)
        
        # 标准化数据
        X_scaled = self.scaler.transform(X_reshaped).reshape(X.shape)
        y_scaled = self.scaler.transform(y_reshaped).reshape(y.shape)
        
        # 按7:3划分数据集
        split_idx = int(len(X) * 0.7)
        
        self.X_train = X_scaled[:split_idx]
        self.X_test = X_scaled[split_idx:]
        self.y_train = y_scaled[:split_idx]
        self.y_test = y_scaled[split_idx:]
        
        print(f"训练集大小: {len(self.X_train)} (70%)")
        print(f"测试集大小: {len(self.X_test)} (30%)")
        
        # 转换为PyTorch张量
        self.X_train = torch.FloatTensor(self.X_train).unsqueeze(-1)  # [batch, seq_len, 1]
        self.X_test = torch.FloatTensor(self.X_test).unsqueeze(-1)
        self.y_train = torch.FloatTensor(self.y_train)  # [batch, pred_len]
        self.y_test = torch.FloatTensor(self.y_test)
        
    def train_model(self, epochs=50, batch_size=32, learning_rate=0.001):
        """训练模型"""
        print("开始训练简化版CALF模型...")
        
        self.model.to(self.device)
        
        # 创建数据加载器
        train_dataset = TensorDataset(self.X_train, self.y_train)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        
        # 优化器和损失函数
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=learning_rate, weight_decay=0.01)
        criterion = nn.MSELoss()
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
        
        # 计算模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        print(f"模型参数数量: {total_params:,}")
        
        # 训练循环
        self.model.train()
        train_losses = []
        
        for epoch in range(epochs):
            epoch_loss = 0.0
            batch_count = 0
            
            for batch_x, batch_y in train_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                
                # 前向传播
                outputs = self.model(batch_x)
                
                # 计算损失
                loss = criterion(outputs, batch_y)
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                epoch_loss += loss.item()
                batch_count += 1
            
            scheduler.step()
            
            avg_loss = epoch_loss / batch_count
            train_losses.append(avg_loss)
            
            if (epoch + 1) % 10 == 0:
                print(f"Epoch [{epoch+1}/{epochs}], Loss: {avg_loss:.6f}, LR: {scheduler.get_last_lr()[0]:.6f}")
        
        print("模型训练完成！")
        return train_losses
        
    def evaluate_model(self):
        """评估模型"""
        print("正在评估模型...")
        
        self.model.eval()
        
        with torch.no_grad():
            # 测试集预测
            X_test_device = self.X_test.to(self.device)
            y_pred_scaled = self.model(X_test_device).cpu().numpy()
            
            # 反标准化
            y_test_original = self.scaler.inverse_transform(
                self.y_test.numpy().reshape(-1, 1)
            ).reshape(self.y_test.shape)
            
            y_pred_original = self.scaler.inverse_transform(
                y_pred_scaled.reshape(-1, 1)
            ).reshape(y_pred_scaled.shape)
        
        # 计算评估指标
        results = {}
        
        # 逐天评估
        for day in range(self.pred_len):
            y_true_day = y_test_original[:, day]
            y_pred_day = y_pred_original[:, day]
            
            mae = mean_absolute_error(y_true_day, y_pred_day)
            mse = mean_squared_error(y_true_day, y_pred_day)
            r2 = r2_score(y_true_day, y_pred_day)
            mape = np.mean(np.abs((y_true_day - y_pred_day) / y_true_day)) * 100
            
            results[f'day_{day+1}'] = {
                'MAE': mae,
                'MSE': mse,
                'R2': r2,
                'MAPE': mape
            }
            
            print(f"第{day+1}天预测 - MAE: {mae:.2f}, R²: {r2:.4f}, MAPE: {mape:.1f}%")
        
        # 整体评估
        y_true_all = y_test_original.flatten()
        y_pred_all = y_pred_original.flatten()
        
        overall_mae = mean_absolute_error(y_true_all, y_pred_all)
        overall_mse = mean_squared_error(y_true_all, y_pred_all)
        overall_r2 = r2_score(y_true_all, y_pred_all)
        overall_mape = np.mean(np.abs((y_true_all - y_pred_all) / y_true_all)) * 100
        
        results['overall'] = {
            'MAE': overall_mae,
            'MSE': overall_mse,
            'R2': overall_r2,
            'MAPE': overall_mape
        }
        
        print(f"\n整体预测 - MAE: {overall_mae:.2f}, R²: {overall_r2:.4f}, MAPE: {overall_mape:.1f}%")
        
        # 保存结果
        self.results = {
            'daily_results': results,
            'predictions': y_pred_original,
            'actuals': y_test_original
        }
        
        return results
        
    def plot_results(self):
        """绘制预测结果"""
        print("正在生成预测结果图表...")
        
        predictions = self.results['predictions']
        actuals = self.results['actuals']
        
        # 创建子图
        fig, axes = plt.subplots(2, 4, figsize=(20, 10))
        axes = axes.flatten()
        
        # 绘制每天的预测结果
        for day in range(min(self.pred_len, 7)):
            ax = axes[day]
            
            actual = actuals[:, day]
            pred = predictions[:, day]
            
            ax.scatter(actual, pred, alpha=0.6, s=20)
            ax.plot([actual.min(), actual.max()], [actual.min(), actual.max()], 'r--', lw=2)
            ax.set_xlabel('实际径流量')
            ax.set_ylabel('预测径流量')
            ax.set_title(f'第{day+1}天预测 (R²={self.results["daily_results"][f"day_{day+1}"]["R2"]:.3f})')
            ax.grid(True, alpha=0.3)
        
        # 时间序列图
        ax = axes[7]
        
        # 显示最后200个样本的时间序列
        n_samples = min(200, len(actuals))
        x_axis = range(n_samples)
        
        for day in range(min(3, self.pred_len)):  # 显示前3天
            ax.plot(x_axis, actuals[-n_samples:, day], 
                   label=f'实际值-第{day+1}天', alpha=0.7, linestyle='-')
            ax.plot(x_axis, predictions[-n_samples:, day], 
                   label=f'预测值-第{day+1}天', alpha=0.7, linestyle='--')
        
        ax.set_xlabel('样本序号')
        ax.set_ylabel('径流量')
        ax.set_title('时间序列预测对比')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
    def demo_prediction(self):
        """演示预测功能"""
        print(f"\n=== 简化版CALF模型{self.pred_len}天径流预测演示 ===")
        
        # 使用最后一个测试样本进行演示
        demo_input = self.X_test[-1:].to(self.device)
        
        self.model.eval()
        with torch.no_grad():
            demo_pred_scaled = self.model(demo_input).cpu().numpy()
            demo_pred = self.scaler.inverse_transform(
                demo_pred_scaled.reshape(-1, 1)
            ).reshape(demo_pred_scaled.shape)
        
        print(f"输入序列长度: {self.seq_len}天")
        print(f"预测序列长度: {self.pred_len}天")
        print(f"\n未来{self.pred_len}天径流预测:")
        
        for i, pred in enumerate(demo_pred[0], 1):
            print(f"  第{i}天: {pred:.2f}")
        
        return demo_pred[0]

def main():
    """主函数"""
    print("=== 简化版CALF径流预测系统 ===")
    print("数据分割: 70%训练集, 30%测试集")
    
    # 创建预测器
    predictor = SimplifiedCALFPredictor(
        seq_len=96,      # 输入序列长度：96天
        pred_len=7,      # 预测序列长度：7天
        d_model=512,     # 模型维度
        n_heads=8,       # 注意力头数
        n_layers=4       # Transformer层数
    )
    
    # 执行完整流程
    predictor.load_data()
    predictor.prepare_time_series_data()
    
    # 训练模型
    train_losses = predictor.train_model(epochs=50, batch_size=32, learning_rate=0.001)
    
    # 评估模型
    results = predictor.evaluate_model()
    
    # 绘制结果
    predictor.plot_results()
    
    # 演示预测
    demo_pred = predictor.demo_prediction()
    
    print("\n=== 实验完成 ===")
    print("模型架构: 简化版CALF (Transformer-based)")
    print("数据分割: 7:3")
    print("预测任务: 7天径流预测")
    
    return predictor, results, demo_pred

if __name__ == "__main__":
    predictor, results, demo_pred = main()
