"""
长北地区径流预测模型配置
使用1971-2017年日气象数据预测径流量
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class RunoffPredictor:
    def __init__(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        """
        初始化径流预测器
        
        Args:
            data_path: 数据文件路径
        """
        self.data_path = data_path
        self.data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.scaler = StandardScaler()
        self.model = None
        
    def load_and_preprocess_data(self, method='weather_features'):
        """
        加载和预处理数据
        
        Args:
            method: 'weather_features' - 使用气象特征预测径流
                   'time_series' - 使用历史径流数据预测未来径流
        """
        print("正在加载数据...")
        self.data = pd.read_csv(self.data_path)
        
        # 处理缺失值
        self.data = self.data.dropna()
        
        # 转换日期格式
        self.data['DATE'] = pd.to_datetime(self.data['DATE'])
        
        if method == 'weather_features':
            self._prepare_weather_features()
        elif method == 'time_series':
            self._prepare_time_series_features()
        else:
            raise ValueError("method must be 'weather_features' or 'time_series'")
            
        print(f"数据预处理完成，特征数量: {self.X_train.shape[1]}")
        
    def _prepare_weather_features(self):
        """
        方案1: 使用气象特征预测径流
        特征包括：日期特征 + 所有气象变量
        """
        print("使用气象特征预测径流...")
        
        # 提取日期特征
        self.data['YEAR'] = self.data['DATE'].dt.year
        self.data['MONTH'] = self.data['DATE'].dt.month
        self.data['DAY'] = self.data['DATE'].dt.day
        self.data['DAY_OF_YEAR'] = self.data['DATE'].dt.dayofyear
        
        # 选择特征（除了RUNOFF和原始DATE）
        feature_columns = ['YEAR', 'MONTH', 'DAY', 'DAY_OF_YEAR', 
                          'TEMP', 'DEWP', 'SLP', 'VISIB', 'WDSP', 
                          'MXSPD', 'MAX', 'MIN', 'PRCP']
        
        X = self.data[feature_columns]
        y = self.data['RUNOFF']
        
        # 分割训练集和测试集
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, shuffle=True
        )
        
        # 标准化特征
        self.X_train = self.scaler.fit_transform(self.X_train)
        self.X_test = self.scaler.transform(self.X_test)
        
    def _prepare_time_series_features(self):
        """
        方案2: 使用历史径流数据预测未来径流（时间序列）
        创建滞后特征
        """
        print("使用时间序列方法预测径流...")
        
        # 按日期排序
        self.data = self.data.sort_values('DATE').reset_index(drop=True)
        
        # 创建滞后特征
        lag_days = [1, 2, 3, 7, 14, 30]  # 1天、2天、3天、1周、2周、1月前的径流
        
        for lag in lag_days:
            self.data[f'RUNOFF_LAG_{lag}'] = self.data['RUNOFF'].shift(lag)
        
        # 创建移动平均特征
        windows = [3, 7, 14, 30]
        for window in windows:
            self.data[f'RUNOFF_MA_{window}'] = self.data['RUNOFF'].rolling(window=window).mean()
        
        # 添加日期特征
        self.data['MONTH'] = self.data['DATE'].dt.month
        self.data['DAY_OF_YEAR'] = self.data['DATE'].dt.dayofyear
        
        # 选择特征
        feature_columns = [f'RUNOFF_LAG_{lag}' for lag in lag_days] + \
                         [f'RUNOFF_MA_{window}' for window in windows] + \
                         ['MONTH', 'DAY_OF_YEAR']
        
        # 删除包含NaN的行
        self.data = self.data.dropna()
        
        X = self.data[feature_columns]
        y = self.data['RUNOFF']
        
        # 时间序列分割：前80%作为训练集，后20%作为测试集
        split_idx = int(len(X) * 0.8)
        self.X_train = X.iloc[:split_idx]
        self.X_test = X.iloc[split_idx:]
        self.y_train = y.iloc[:split_idx]
        self.y_test = y.iloc[split_idx:]
        
        # 标准化特征
        self.X_train = self.scaler.fit_transform(self.X_train)
        self.X_test = self.scaler.transform(self.X_test)
        
    def train_model(self, model_type='random_forest'):
        """
        训练模型
        
        Args:
            model_type: 'random_forest', 'linear_regression'
        """
        print(f"正在训练{model_type}模型...")
        
        if model_type == 'random_forest':
            self.model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
        elif model_type == 'linear_regression':
            self.model = LinearRegression()
        else:
            raise ValueError("model_type must be 'random_forest' or 'linear_regression'")
        
        self.model.fit(self.X_train, self.y_train)
        print("模型训练完成！")
        
    def evaluate_model(self):
        """
        评估模型性能
        """
        # 预测
        y_train_pred = self.model.predict(self.X_train)
        y_test_pred = self.model.predict(self.X_test)
        
        # 计算评估指标
        train_mse = mean_squared_error(self.y_train, y_train_pred)
        test_mse = mean_squared_error(self.y_test, y_test_pred)
        train_r2 = r2_score(self.y_train, y_train_pred)
        test_r2 = r2_score(self.y_test, y_test_pred)
        train_mae = mean_absolute_error(self.y_train, y_train_pred)
        test_mae = mean_absolute_error(self.y_test, y_test_pred)
        
        print("\n=== 模型评估结果 ===")
        print(f"训练集 - MSE: {train_mse:.2f}, R²: {train_r2:.4f}, MAE: {train_mae:.2f}")
        print(f"测试集 - MSE: {test_mse:.2f}, R²: {test_r2:.4f}, MAE: {test_mae:.2f}")
        
        return {
            'train_mse': train_mse, 'test_mse': test_mse,
            'train_r2': train_r2, 'test_r2': test_r2,
            'train_mae': train_mae, 'test_mae': test_mae,
            'y_test_pred': y_test_pred
        }
        
    def plot_results(self, results):
        """
        绘制预测结果
        """
        plt.figure(figsize=(12, 8))
        
        # 预测vs实际值散点图
        plt.subplot(2, 2, 1)
        plt.scatter(self.y_test, results['y_test_pred'], alpha=0.6)
        plt.plot([self.y_test.min(), self.y_test.max()], 
                [self.y_test.min(), self.y_test.max()], 'r--', lw=2)
        plt.xlabel('实际径流量')
        plt.ylabel('预测径流量')
        plt.title(f'预测vs实际 (R² = {results["test_r2"]:.4f})')
        
        # 残差图
        plt.subplot(2, 2, 2)
        residuals = self.y_test - results['y_test_pred']
        plt.scatter(results['y_test_pred'], residuals, alpha=0.6)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel('预测径流量')
        plt.ylabel('残差')
        plt.title('残差分布')
        
        # 时间序列图（显示最后500个点）
        plt.subplot(2, 1, 2)
        n_points = min(500, len(self.y_test))
        plt.plot(range(n_points), self.y_test.iloc[-n_points:], 
                label='实际值', alpha=0.7)
        plt.plot(range(n_points), results['y_test_pred'][-n_points:], 
                label='预测值', alpha=0.7)
        plt.xlabel('时间点')
        plt.ylabel('径流量')
        plt.title('径流量预测时间序列（最后500个点）')
        plt.legend()
        
        plt.tight_layout()
        plt.show()

# 使用示例
if __name__ == "__main__":
    # 创建预测器实例
    predictor = RunoffPredictor()
    
    print("=== 方案1: 使用气象特征预测径流 ===")
    predictor.load_and_preprocess_data(method='weather_features')
    predictor.train_model(model_type='random_forest')
    results1 = predictor.evaluate_model()
    predictor.plot_results(results1)
    
    print("\n" + "="*50)
    print("=== 方案2: 使用历史径流数据预测未来径流 ===")
    predictor2 = RunoffPredictor()
    predictor2.load_and_preprocess_data(method='time_series')
    predictor2.train_model(model_type='random_forest')
    results2 = predictor2.evaluate_model()
    predictor2.plot_results(results2)
