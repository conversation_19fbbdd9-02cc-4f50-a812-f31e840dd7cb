"""
修复版多步径流预测系统
专门用于3天和5天预测，避免重复运行问题
"""

import pandas as pd
import numpy as np
import json
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

class FixedMultiStepPredictor:
    """修复版多步径流预测器"""
    
    def __init__(self, forecast_days=3, use_enhanced_features=True):
        self.forecast_days = forecast_days
        self.use_enhanced_features = use_enhanced_features
        self.data = None
        self.models = {}
        self.scaler = None
        self.feature_columns = []
        self.results = {}
        
    def load_data(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        """加载数据"""
        print(f"正在加载数据用于{self.forecast_days}天预测...")
        self.data = pd.read_csv(data_path)
        self.data['DATE'] = pd.to_datetime(self.data['DATE'])
        self.data = self.data.sort_values('DATE').reset_index(drop=True)
        self.data = self.data.dropna()
        print(f"数据加载完成：{len(self.data)}条记录")
        
    def create_features(self):
        """创建特征"""
        print("正在创建特征...")
        
        if self.use_enhanced_features:
            # 增强特征集（用于更好的性能）
            lag_days = [1, 2, 3, 7, 14, 21, 30]
            ma_windows = [3, 7, 14, 30]
            std_windows = [7, 14, 30]
            change_lags = [1, 7, 14]
        else:
            # 基础特征集
            lag_days = [1, 2, 3, 7, 14, 30]
            ma_windows = [3, 7, 14]
            std_windows = [7, 14]
            change_lags = [1, 7]
        
        # 滞后特征
        for lag in lag_days:
            self.data[f'LAG_{lag}'] = self.data['RUNOFF'].shift(lag)
        
        # 移动平均特征
        for window in ma_windows:
            self.data[f'MA_{window}'] = self.data['RUNOFF'].rolling(window=window).mean()
        
        # 移动标准差特征
        for window in std_windows:
            self.data[f'STD_{window}'] = self.data['RUNOFF'].rolling(window=window).std()
        
        # 变化率特征
        for lag in change_lags:
            self.data[f'CHANGE_{lag}'] = self.data['RUNOFF'] - self.data['RUNOFF'].shift(lag)
            self.data[f'PCT_CHANGE_{lag}'] = self.data['RUNOFF'].pct_change(periods=lag)
        
        # 极值特征
        for window in [7, 14, 30]:
            self.data[f'MAX_{window}'] = self.data['RUNOFF'].rolling(window=window).max()
            self.data[f'MIN_{window}'] = self.data['RUNOFF'].rolling(window=window).min()
        
        # 时间特征
        self.data['MONTH'] = self.data['DATE'].dt.month
        self.data['DAY_OF_YEAR'] = self.data['DATE'].dt.dayofyear
        
        # 周期性编码
        self.data['MONTH_SIN'] = np.sin(2 * np.pi * self.data['MONTH'] / 12)
        self.data['MONTH_COS'] = np.cos(2 * np.pi * self.data['MONTH'] / 12)
        self.data['DAY_SIN'] = np.sin(2 * np.pi * self.data['DAY_OF_YEAR'] / 365)
        self.data['DAY_COS'] = np.cos(2 * np.pi * self.data['DAY_OF_YEAR'] / 365)
        
        # 创建目标变量
        for day in range(1, self.forecast_days + 1):
            self.data[f'TARGET_{day}'] = self.data['RUNOFF'].shift(-day)
        
        # 删除缺失值
        self.data = self.data.dropna()
        
        # 定义特征列
        self.feature_columns = [col for col in self.data.columns 
                               if col not in ['DATE', 'RUNOFF'] and not col.startswith('TARGET_')]
        
        print(f"特征创建完成：{len(self.feature_columns)}个特征")
        
    def train_models(self, n_estimators=None, max_depth=None):
        """训练模型"""
        print("正在训练模型...")
        
        # 根据预测天数调整模型参数
        if n_estimators is None:
            n_estimators = 150 if self.forecast_days >= 5 else 100
        if max_depth is None:
            max_depth = 15 if self.forecast_days >= 5 else 12
        
        X = self.data[self.feature_columns]
        
        # 数据分割
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        
        # 特征标准化
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 保存测试数据
        self.X_test_scaled = X_test_scaled
        self.test_start_idx = split_idx
        
        # 为每一天训练独立模型
        for day in range(1, self.forecast_days + 1):
            print(f"  训练第{day}天预测模型...")
            
            y_train = self.data[f'TARGET_{day}'].iloc[:split_idx]
            
            model = RandomForestRegressor(
                n_estimators=n_estimators,
                max_depth=max_depth,
                min_samples_split=3,
                min_samples_leaf=2,
                random_state=42 + day,
                n_jobs=-1
            )
            
            model.fit(X_train_scaled, y_train)
            self.models[f'day_{day}'] = model
        
        print("模型训练完成！")
        
    def predict(self, input_features):
        """进行多步预测"""
        input_scaled = self.scaler.transform(input_features.reshape(1, -1))
        
        predictions = []
        for day in range(1, self.forecast_days + 1):
            model = self.models[f'day_{day}']
            pred = model.predict(input_scaled)[0]
            predictions.append(pred)
        
        return np.array(predictions)
        
    def evaluate_models(self):
        """评估模型性能"""
        print(f"\n=== {self.forecast_days}天预测模型评估结果 ===")
        
        # 预测测试集
        test_predictions = []
        for i in range(len(self.X_test_scaled)):
            pred = self.predict(self.X_test_scaled[i])
            test_predictions.append(pred)
        
        test_predictions = np.array(test_predictions)
        
        # 计算每天的评估指标
        daily_results = {}
        
        for day in range(self.forecast_days):
            y_true = self.data[f'TARGET_{day+1}'].iloc[self.test_start_idx:].values
            y_pred = test_predictions[:, day]
            
            # 确保长度一致
            min_len = min(len(y_true), len(y_pred))
            y_true = y_true[:min_len]
            y_pred = y_pred[:min_len]
            
            mae = mean_absolute_error(y_true, y_pred)
            mse = mean_squared_error(y_true, y_pred)
            r2 = r2_score(y_true, y_pred)
            mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
            
            daily_results[f'day_{day+1}'] = {
                'MAE': mae,
                'MSE': mse,
                'R2': r2,
                'MAPE': mape
            }
            
            print(f"第{day+1}天预测 - MAE: {mae:.2f}, R²: {r2:.4f}, MAPE: {mape:.1f}%")
        
        # 整体评估
        all_true = []
        all_pred = []
        
        for day in range(self.forecast_days):
            y_true = self.data[f'TARGET_{day+1}'].iloc[self.test_start_idx:].values
            y_pred = test_predictions[:, day]
            min_len = min(len(y_true), len(y_pred))
            all_true.extend(y_true[:min_len])
            all_pred.extend(y_pred[:min_len])
        
        overall_mae = mean_absolute_error(all_true, all_pred)
        overall_r2 = r2_score(all_true, all_pred)
        overall_mape = np.mean(np.abs((np.array(all_true) - np.array(all_pred)) / np.array(all_true))) * 100
        
        overall_results = {
            'MAE': overall_mae,
            'R2': overall_r2,
            'MAPE': overall_mape
        }
        
        print(f"\n整体预测 - MAE: {overall_mae:.2f}, R²: {overall_r2:.4f}, MAPE: {overall_mape:.1f}%")
        
        # 保存结果
        self.results = {
            'daily_results': daily_results,
            'overall_results': overall_results,
            'predictions': test_predictions
        }
        
        return self.results
        
    def demo_prediction(self):
        """演示预测功能"""
        print(f"\n=== {self.forecast_days}天预测演示 ===")
        
        # 使用最新数据进行演示
        latest_data = self.data.iloc[-1]
        input_features = latest_data[self.feature_columns].values
        
        print(f"基于 {latest_data['DATE'].strftime('%Y-%m-%d')} 的数据预测:")
        print(f"当日径流量: {latest_data['RUNOFF']:.2f}")
        
        # 进行预测
        predictions = self.predict(input_features)
        
        print(f"\n未来{self.forecast_days}天预测结果:")
        for i, pred in enumerate(predictions, 1):
            future_date = latest_data['DATE'] + pd.Timedelta(days=i)
            print(f"  第{i}天 ({future_date.strftime('%Y-%m-%d')}): {pred:.2f}")
        
        return predictions
        
    def get_feature_importance(self):
        """获取特征重要性"""
        print(f"\n=== {self.forecast_days}天预测特征重要性分析 ===")
        
        # 计算所有模型的平均特征重要性
        importance_sum = np.zeros(len(self.feature_columns))
        
        for day in range(1, self.forecast_days + 1):
            model = self.models[f'day_{day}']
            importance_sum += model.feature_importances_
        
        avg_importance = importance_sum / self.forecast_days
        
        # 创建重要性DataFrame
        importance_df = pd.DataFrame({
            'feature': self.feature_columns,
            'importance': avg_importance
        }).sort_values('importance', ascending=False)
        
        print("Top 10 重要特征:")
        for i, row in importance_df.head(10).iterrows():
            print(f"  {row['feature']}: {row['importance']:.4f}")
        
        return importance_df

def run_experiment(forecast_days, use_enhanced_features=True):
    """运行实验"""
    print(f"\n{'='*60}")
    print(f"开始 {forecast_days} 天径流预测实验")
    print(f"增强特征: {'是' if use_enhanced_features else '否'}")
    print(f"{'='*60}")
    
    # 创建预测器
    predictor = FixedMultiStepPredictor(
        forecast_days=forecast_days,
        use_enhanced_features=use_enhanced_features
    )
    
    # 执行完整流程
    predictor.load_data()
    predictor.create_features()
    predictor.train_models()
    
    # 评估和分析
    results = predictor.evaluate_models()
    predictions = predictor.demo_prediction()
    importance = predictor.get_feature_importance()
    
    return predictor, results, predictions, importance

if __name__ == "__main__":
    # 运行3天预测实验
    predictor_3day, results_3day, pred_3day, importance_3day = run_experiment(
        forecast_days=3, 
        use_enhanced_features=True
    )
    
    # 运行5天预测实验
    predictor_5day, results_5day, pred_5day, importance_5day = run_experiment(
        forecast_days=5, 
        use_enhanced_features=True
    )
    
    # 对比总结
    print(f"\n{'='*60}")
    print("实验对比总结")
    print(f"{'='*60}")
    
    print("\n3天预测 vs 5天预测性能对比:")
    print(f"3天预测整体R²: {results_3day['overall_results']['R2']:.4f}")
    print(f"5天预测整体R²: {results_5day['overall_results']['R2']:.4f}")
    print(f"3天预测整体MAE: {results_3day['overall_results']['MAE']:.2f}")
    print(f"5天预测整体MAE: {results_5day['overall_results']['MAE']:.2f}")
    
    print("\n实验完成！")
