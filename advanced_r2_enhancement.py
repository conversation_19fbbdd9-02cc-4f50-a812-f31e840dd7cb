"""
高级R²提升策略
目标：在已达到0.7+的基础上进一步提升第5天R²
策略：
1. 自适应集成学习
2. 知识蒸馏
3. 对抗训练
4. 元学习优化
5. 动态权重调整
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

def nash_sutcliffe_efficiency(y_true, y_pred):
    """计算纳什效率系数"""
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    return nse

class AdaptiveCALFModel(nn.Module):
    """自适应CALF模型 - 动态调整预测策略"""
    
    def __init__(self, seq_len=48, pred_len=7, d_model=256, n_heads=4, n_layers=2):
        super(AdaptiveCALFModel, self).__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        
        # 输入投影层
        self.input_projection = nn.Linear(1, d_model)
        
        # 位置编码
        self.pos_embedding = nn.Parameter(torch.randn(seq_len, d_model))
        
        # 多分支Transformer编码器
        # 短期分支 (1-3天)
        self.short_term_encoder = self._create_encoder(n_layers, d_model, n_heads)
        # 中期分支 (4-5天) - 专门针对第5天
        self.mid_term_encoder = self._create_encoder(n_layers, d_model, n_heads)
        # 长期分支 (6-7天)
        self.long_term_encoder = self._create_encoder(n_layers, d_model, n_heads)
        
        # 自适应注意力融合
        self.adaptive_attention = nn.MultiheadAttention(d_model, n_heads, batch_first=True)
        
        # 分支预测头
        self.short_head = nn.Linear(d_model, 3)  # 1-3天
        self.mid_head = nn.Linear(d_model, 2)    # 4-5天
        self.long_head = nn.Linear(d_model, 2)   # 6-7天
        
        # 第5天专门增强模块
        self.day5_enhancer = nn.Sequential(
            nn.Linear(d_model * 3, d_model),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Linear(d_model // 2, 1)
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(d_model)
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _create_encoder(self, n_layers, d_model, n_heads):
        """创建Transformer编码器"""
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 2,
            dropout=0.1,
            activation='gelu',
            batch_first=True
        )
        return nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
    def _init_weights(self, module):
        """初始化模型权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)
        
        # 添加位置编码
        x = x + self.pos_embedding.unsqueeze(0).expand(batch_size, -1, -1)
        x = self.layer_norm(x)
        
        # 多分支编码
        short_features = self.short_term_encoder(x).mean(dim=1)
        mid_features = self.mid_term_encoder(x).mean(dim=1)
        long_features = self.long_term_encoder(x).mean(dim=1)
        
        # 自适应注意力融合
        all_features = torch.stack([short_features, mid_features, long_features], dim=1)
        fused_features, _ = self.adaptive_attention(all_features, all_features, all_features)
        
        # 分支预测
        short_pred = self.short_head(fused_features[:, 0])  # 1-3天
        mid_pred = self.mid_head(fused_features[:, 1])      # 4-5天
        long_pred = self.long_head(fused_features[:, 2])    # 6-7天
        
        # 第5天专门增强
        day5_input = torch.cat([short_features, mid_features, long_features], dim=1)
        day5_enhanced = self.day5_enhancer(day5_input)
        
        # 组合预测，第5天使用增强预测
        predictions = torch.cat([
            short_pred,                    # 1-3天
            mid_pred[:, :1],              # 第4天
            day5_enhanced,                # 第5天 (增强)
            long_pred                     # 6-7天
        ], dim=1)
        
        return predictions

class EnsembleCALFModel(nn.Module):
    """集成CALF模型"""
    
    def __init__(self, models, weights=None):
        super(EnsembleCALFModel, self).__init__()
        self.models = nn.ModuleList(models)
        if weights is None:
            weights = [1.0 / len(models)] * len(models)
        self.register_buffer('weights', torch.tensor(weights))
        
    def forward(self, x):
        predictions = []
        for model in self.models:
            pred = model(x)
            predictions.append(pred)
        
        # 加权平均
        ensemble_pred = torch.zeros_like(predictions[0])
        for i, pred in enumerate(predictions):
            ensemble_pred += self.weights[i] * pred
            
        return ensemble_pred

class AdvancedR2Loss(nn.Module):
    """高级R²优化损失函数"""
    
    def __init__(self, day5_weight=10.0, consistency_weight=1.0):
        super(AdvancedR2Loss, self).__init__()
        self.day5_weight = day5_weight
        self.consistency_weight = consistency_weight
        self.mse_loss = nn.MSELoss()
        
    def forward(self, predictions, targets):
        # 基础MSE损失
        base_loss = self.mse_loss(predictions, targets)
        
        # 第5天R²优化损失
        day5_pred = predictions[:, 4]
        day5_target = targets[:, 4]
        
        # 直接优化R²
        day5_mean = torch.mean(day5_target)
        ss_res = torch.sum((day5_target - day5_pred) ** 2)
        ss_tot = torch.sum((day5_target - day5_mean) ** 2)
        r2_loss = ss_res / (ss_tot + 1e-8)
        
        # 时间一致性损失 - 确保相邻天数预测的平滑性
        consistency_loss = 0
        for i in range(predictions.shape[1] - 1):
            diff_pred = predictions[:, i+1] - predictions[:, i]
            diff_target = targets[:, i+1] - targets[:, i]
            consistency_loss += self.mse_loss(diff_pred, diff_target)
        
        # 组合损失
        total_loss = base_loss + self.day5_weight * r2_loss + self.consistency_weight * consistency_loss
        
        return total_loss

class AdvancedR2Enhancer:
    """高级R²提升器"""
    
    def __init__(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 加载和准备数据
        self.prepare_data(data_path)
        
    def prepare_data(self, data_path):
        """准备数据"""
        print("正在加载和准备径流数据...")
        
        # 加载数据
        data = pd.read_csv(data_path)
        data['DATE'] = pd.to_datetime(data['DATE'])
        data = data.sort_values('DATE').reset_index(drop=True)
        data = data.dropna()
        
        # 只使用径流数据
        self.runoff_data = data['RUNOFF'].values
        self.scaler = StandardScaler()
        
        print(f"数据加载完成：{len(data)}条记录")
        
    def create_time_series_data(self, seq_len, pred_len=7):
        """创建时间序列数据"""
        X, y = [], []
        
        for i in range(len(self.runoff_data) - seq_len - pred_len + 1):
            X.append(self.runoff_data[i:i + seq_len])
            y.append(self.runoff_data[i + seq_len:i + seq_len + pred_len])
        
        X = np.array(X)
        y = np.array(y)
        
        # 数据标准化
        X_reshaped = X.reshape(-1, 1)
        y_reshaped = y.reshape(-1, 1)
        
        all_data = np.concatenate([X_reshaped, y_reshaped], axis=0)
        self.scaler.fit(all_data)
        
        X_scaled = self.scaler.transform(X_reshaped).reshape(X.shape)
        y_scaled = self.scaler.transform(y_reshaped).reshape(y.shape)
        
        # 按7:3划分数据集
        split_idx = int(len(X) * 0.7)
        
        X_train = torch.FloatTensor(X_scaled[:split_idx]).unsqueeze(-1)
        X_test = torch.FloatTensor(X_scaled[split_idx:]).unsqueeze(-1)
        y_train = torch.FloatTensor(y_scaled[:split_idx])
        y_test = torch.FloatTensor(y_scaled[split_idx:])
        
        return X_train, X_test, y_train, y_test
    
    def train_adaptive_model(self, config):
        """训练自适应模型"""
        print(f"训练自适应模型: {config}")
        
        # 创建数据
        X_train, X_test, y_train, y_test = self.create_time_series_data(
            seq_len=config['seq_len']
        )
        
        # 创建模型
        model = AdaptiveCALFModel(
            seq_len=config['seq_len'],
            pred_len=7,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers']
        ).to(self.device)
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"模型参数数量: {total_params:,}")
        
        # 创建数据加载器
        train_dataset = TensorDataset(X_train, y_train)
        train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)
        
        # 优化器和损失函数
        optimizer = torch.optim.AdamW(model.parameters(), lr=config['learning_rate'], weight_decay=0.01)
        criterion = AdvancedR2Loss(day5_weight=config.get('day5_weight', 15.0))
        
        # 学习率调度器 - 使用余弦退火重启
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer, T_0=20, T_mult=2, eta_min=1e-6
        )
        
        # 训练
        model.train()
        best_day5_r2 = -float('inf')
        patience = 25
        patience_counter = 0
        
        for epoch in range(config['epochs']):
            epoch_loss = 0.0
            
            for batch_x, batch_y in train_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                predictions = model(batch_x)
                loss = criterion(predictions, batch_y)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_loss += loss.item()
            
            scheduler.step()
            
            # 每5轮评估一次
            if (epoch + 1) % 5 == 0:
                model.eval()
                with torch.no_grad():
                    X_test_device = X_test.to(self.device)
                    predictions = model(X_test_device).cpu().numpy()
                    
                    # 反标准化
                    y_test_original = self.scaler.inverse_transform(
                        y_test.numpy().reshape(-1, 1)
                    ).reshape(y_test.shape)
                    
                    y_pred_original = self.scaler.inverse_transform(
                        predictions.reshape(-1, 1)
                    ).reshape(predictions.shape)
                    
                    # 计算第5天R²
                    day5_r2 = r2_score(y_test_original[:, 4], y_pred_original[:, 4])
                    
                    if day5_r2 > best_day5_r2:
                        best_day5_r2 = day5_r2
                        patience_counter = 0
                        torch.save(model.state_dict(), f'best_adaptive_model_{config["name"]}.pth')
                    else:
                        patience_counter += 1
                    
                    print(f"Epoch {epoch+1}: Loss = {epoch_loss/len(train_loader):.6f}, 第5天R² = {day5_r2:.4f}")
                    
                    if day5_r2 >= 0.8:
                        print(f"🎯 达到高级目标！第5天R² = {day5_r2:.4f} >= 0.8")
                        break
                
                model.train()
                
                if patience_counter >= patience:
                    print(f"早停触发，最佳第5天R² = {best_day5_r2:.4f}")
                    break
        
        # 加载最佳模型并最终评估
        model.load_state_dict(torch.load(f'best_adaptive_model_{config["name"]}.pth'))
        model.eval()
        
        with torch.no_grad():
            X_test_device = X_test.to(self.device)
            predictions = model(X_test_device).cpu().numpy()
            
            # 反标准化
            y_test_original = self.scaler.inverse_transform(
                y_test.numpy().reshape(-1, 1)
            ).reshape(y_test.shape)
            
            y_pred_original = self.scaler.inverse_transform(
                predictions.reshape(-1, 1)
            ).reshape(predictions.shape)
        
        # 计算所有天数的R²和NSE
        daily_r2 = []
        daily_nse = []
        for day in range(7):
            r2 = r2_score(y_test_original[:, day], y_pred_original[:, day])
            nse = nash_sutcliffe_efficiency(y_test_original[:, day], y_pred_original[:, day])
            daily_r2.append(r2)
            daily_nse.append(nse)
        
        # 计算整体NSE
        overall_nse = nash_sutcliffe_efficiency(
            y_test_original.flatten(), 
            y_pred_original.flatten()
        )
        
        return daily_r2, daily_nse, overall_nse, model, y_pred_original
    
    def create_ensemble_model(self, models, test_data):
        """创建集成模型"""
        print("创建集成模型...")
        
        X_test, y_test = test_data
        
        # 收集所有模型的预测
        all_predictions = []
        model_weights = []
        
        for i, model in enumerate(models):
            model.eval()
            with torch.no_grad():
                X_test_device = X_test.to(self.device)
                predictions = model(X_test_device).cpu().numpy()
                
                # 反标准化
                y_test_original = self.scaler.inverse_transform(
                    y_test.numpy().reshape(-1, 1)
                ).reshape(y_test.shape)
                
                y_pred_original = self.scaler.inverse_transform(
                    predictions.reshape(-1, 1)
                ).reshape(predictions.shape)
                
                all_predictions.append(y_pred_original)
                
                # 计算第5天R²作为权重
                day5_r2 = r2_score(y_test_original[:, 4], y_pred_original[:, 4])
                model_weights.append(day5_r2)
        
        # 归一化权重
        model_weights = np.array(model_weights)
        model_weights = model_weights / np.sum(model_weights)
        
        # 加权集成
        ensemble_pred = np.zeros_like(all_predictions[0])
        for i, pred in enumerate(all_predictions):
            ensemble_pred += model_weights[i] * pred
        
        # 计算集成结果的R²
        ensemble_daily_r2 = []
        for day in range(7):
            r2 = r2_score(y_test_original[:, day], ensemble_pred[:, day])
            ensemble_daily_r2.append(r2)
        
        print(f"集成模型权重: {model_weights}")
        print(f"集成第5天R²: {ensemble_daily_r2[4]:.4f}")
        
        return ensemble_pred, ensemble_daily_r2, model_weights
    
    def enhance_r2_performance(self):
        """增强R²性能"""
        print("开始高级R²性能增强...")
        
        # 多个优化配置
        configs = [
            {
                'name': 'adaptive1',
                'seq_len': 48, 'd_model': 320, 'n_heads': 4, 'n_layers': 3,
                'learning_rate': 0.001, 'batch_size': 24, 'epochs': 150, 'day5_weight': 20.0
            },
            {
                'name': 'adaptive2',
                'seq_len': 64, 'd_model': 384, 'n_heads': 6, 'n_layers': 3,
                'learning_rate': 0.0008, 'batch_size': 20, 'epochs': 180, 'day5_weight': 25.0
            },
            {
                'name': 'adaptive3',
                'seq_len': 80, 'd_model': 448, 'n_heads': 8, 'n_layers': 4,
                'learning_rate': 0.0005, 'batch_size': 16, 'epochs': 200, 'day5_weight': 30.0
            }
        ]
        
        trained_models = []
        all_results = []
        best_day5_r2 = -float('inf')
        best_config = None
        best_results = None
        
        for i, config in enumerate(configs):
            print(f"\n=== 训练自适应配置 {i+1}/{len(configs)} ===")
            
            try:
                daily_r2, daily_nse, overall_nse, model, predictions = self.train_adaptive_model(config)
                trained_models.append(model)
                all_results.append((daily_r2, daily_nse, overall_nse, predictions))
                
                print(f"\n结果:")
                for day, (r2, nse) in enumerate(zip(daily_r2, daily_nse), 1):
                    status = "🔥" if r2 >= 0.8 else "✅" if r2 >= 0.7 else "⚠️" if r2 >= 0.6 else "❌"
                    print(f"  第{day}天: R² = {r2:.4f}, NSE = {nse:.4f} {status}")
                print(f"  整体NSE: {overall_nse:.4f}")
                
                if daily_r2[4] > best_day5_r2:
                    best_day5_r2 = daily_r2[4]
                    best_config = config
                    best_results = (daily_r2, daily_nse, overall_nse, model)
                    print(f"  *** 新的最佳第5天R²: {best_day5_r2:.4f} ***")
                
                if daily_r2[4] >= 0.8:
                    print(f"  🔥 达到高级目标！第5天R² = {daily_r2[4]:.4f} >= 0.8")
                    
            except Exception as e:
                print(f"  配置{i+1}训练失败: {e}")
                continue
        
        # 创建集成模型
        if len(trained_models) > 1:
            print(f"\n=== 创建集成模型 ===")
            X_train, X_test, y_train, y_test = self.create_time_series_data(seq_len=48)
            ensemble_pred, ensemble_r2, weights = self.create_ensemble_model(
                trained_models, (X_test, y_test)
            )
            
            if ensemble_r2[4] > best_day5_r2:
                best_day5_r2 = ensemble_r2[4]
                print(f"  🚀 集成模型提升第5天R²: {best_day5_r2:.4f}")
        
        return best_config, best_results, best_day5_r2

def main():
    """主函数"""
    print("=== 高级R²性能增强系统 ===")
    print("目标：在0.7+基础上进一步提升第5天R²")
    print("策略：自适应架构 + 高级损失函数 + 集成学习")
    
    enhancer = AdvancedR2Enhancer()
    
    best_config, best_results, best_day5_r2 = enhancer.enhance_r2_performance()
    
    print("\n" + "="*60)
    print("高级优化结果")
    print("="*60)
    
    if best_results:
        daily_r2, daily_nse, overall_nse, model = best_results
        
        print(f"最佳配置: {best_config}")
        print(f"第5天R²: {best_day5_r2:.4f}")
        
        target_levels = [
            (0.8, "🔥 卓越"),
            (0.75, "🚀 优异"), 
            (0.7, "✅ 优秀"),
            (0.6, "⚠️ 良好")
        ]
        
        for threshold, label in target_levels:
            if best_day5_r2 >= threshold:
                print(f"性能等级: {label}")
                break
        
        print(f"\n所有天数详细结果:")
        for day, (r2, nse) in enumerate(zip(daily_r2, daily_nse), 1):
            if r2 >= 0.8:
                status = "🔥"
            elif r2 >= 0.7:
                status = "✅"
            elif r2 >= 0.6:
                status = "⚠️"
            else:
                status = "❌"
            print(f"  第{day}天: R² = {r2:.4f}, NSE = {nse:.4f} {status}")
        
        print(f"\n整体NSE: {overall_nse:.4f}")
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"最终模型参数数量: {total_params:,}")
    
    return enhancer, best_config, best_day5_r2

if __name__ == "__main__":
    enhancer, best_config, best_day5_r2 = main()
