"""
可配置的多步径流预测系统
支持3天、5天等不同预测长度，使用超参数配置文件
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

from hyperparameters_config import HyperparameterConfig, ConfigPresets

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ConfigurableMultiStepPredictor:
    """可配置的多步径流预测器"""
    
    def __init__(self, config: HyperparameterConfig):
        self.config = config
        self.data = None
        self.models = {}
        self.scaler = None
        self.feature_columns = []
        self.target_columns = []
        self.results = {}
        
    def load_data(self):
        """加载数据"""
        print("正在加载数据...")
        self.data = pd.read_csv(self.config.data_config.data_path)
        self.data['DATE'] = pd.to_datetime(self.data['DATE'])
        self.data = self.data.sort_values('DATE').reset_index(drop=True)
        
        # 处理缺失值
        if self.config.data_config.missing_value_strategy == 'drop':
            self.data = self.data.dropna()
        elif self.config.data_config.missing_value_strategy == 'interpolate':
            self.data = self.data.interpolate()
        elif self.config.data_config.missing_value_strategy == 'forward_fill':
            self.data = self.data.fillna(method='ffill')
            
        print(f"数据加载完成：{len(self.data)}条记录")
        
    def create_features(self):
        """根据配置创建特征"""
        print("正在创建特征...")
        fc = self.config.feature_config
        
        # 滞后特征
        for lag in fc.lag_days:
            self.data[f'LAG_{lag}'] = self.data['RUNOFF'].shift(lag)
        
        # 移动平均特征
        for window in fc.ma_windows:
            self.data[f'MA_{window}'] = self.data['RUNOFF'].rolling(window=window).mean()
        
        # 移动标准差特征
        for window in fc.std_windows:
            self.data[f'STD_{window}'] = self.data['RUNOFF'].rolling(window=window).std()
        
        # 变化率特征
        for lag in fc.change_lags:
            self.data[f'CHANGE_{lag}'] = self.data['RUNOFF'] - self.data['RUNOFF'].shift(lag)
            self.data[f'PCT_CHANGE_{lag}'] = self.data['RUNOFF'].pct_change(periods=lag)
        
        # 极值特征
        for window in fc.extrema_windows:
            self.data[f'MAX_{window}'] = self.data['RUNOFF'].rolling(window=window).max()
            self.data[f'MIN_{window}'] = self.data['RUNOFF'].rolling(window=window).min()
            self.data[f'RANGE_{window}'] = self.data[f'MAX_{window}'] - self.data[f'MIN_{window}']
        
        # 时间特征
        if fc.use_month:
            self.data['MONTH'] = self.data['DATE'].dt.month
        if fc.use_day_of_year:
            self.data['DAY_OF_YEAR'] = self.data['DATE'].dt.dayofyear
        if fc.use_week_of_year:
            self.data['WEEK_OF_YEAR'] = self.data['DATE'].dt.isocalendar().week
        if fc.use_season:
            self.data['SEASON'] = self.data['DATE'].dt.month % 12 // 3 + 1
        
        # 周期性编码
        if fc.use_cyclic_encoding:
            if fc.use_month:
                self.data['MONTH_SIN'] = np.sin(2 * np.pi * self.data['MONTH'] / 12)
                self.data['MONTH_COS'] = np.cos(2 * np.pi * self.data['MONTH'] / 12)
            if fc.use_day_of_year:
                self.data['DAY_SIN'] = np.sin(2 * np.pi * self.data['DAY_OF_YEAR'] / 365)
                self.data['DAY_COS'] = np.cos(2 * np.pi * self.data['DAY_OF_YEAR'] / 365)
        
        # 创建目标变量
        forecast_days = self.config.experiment_config.forecast_days
        for day in range(1, forecast_days + 1):
            self.data[f'TARGET_{day}'] = self.data['RUNOFF'].shift(-day)
        
        # 删除缺失值
        self.data = self.data.dropna()
        
        # 定义特征列和目标列
        self.feature_columns = [col for col in self.data.columns 
                               if col not in ['DATE', 'RUNOFF'] and not col.startswith('TARGET_')]
        self.target_columns = [f'TARGET_{day}' for day in range(1, forecast_days + 1)]
        
        print(f"特征创建完成：{len(self.feature_columns)}个特征")
        print(f"特征列表：{self.feature_columns[:10]}..." if len(self.feature_columns) > 10 else f"特征列表：{self.feature_columns}")
        
    def create_model(self, random_state_offset=0):
        """根据配置创建模型"""
        mc = self.config.model_config
        
        if mc.model_type == 'random_forest':
            return RandomForestRegressor(
                n_estimators=mc.n_estimators,
                max_depth=mc.max_depth,
                min_samples_split=mc.min_samples_split,
                min_samples_leaf=mc.min_samples_leaf,
                max_features=mc.max_features,
                bootstrap=mc.bootstrap,
                random_state=mc.random_state + random_state_offset,
                n_jobs=mc.n_jobs
            )
        elif mc.model_type == 'gradient_boosting':
            return GradientBoostingRegressor(
                n_estimators=mc.n_estimators,
                max_depth=mc.max_depth,
                random_state=mc.random_state + random_state_offset
            )
        else:
            raise ValueError(f"不支持的模型类型: {mc.model_type}")
    
    def train_models(self):
        """训练多步预测模型"""
        print("正在训练模型...")
        
        X = self.data[self.feature_columns]
        y = self.data[self.target_columns]
        
        # 数据分割
        split_idx = int(len(X) * self.config.data_config.train_ratio)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        # 特征标准化
        if self.config.data_config.scale_features:
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
        else:
            X_train_scaled = X_train.values
            X_test_scaled = X_test.values
        
        # 保存测试数据
        self.X_test_scaled = X_test_scaled
        self.y_test = y_test
        self.test_start_idx = split_idx
        
        # 为每一天训练独立模型
        forecast_days = self.config.experiment_config.forecast_days
        for day in range(1, forecast_days + 1):
            print(f"  训练第{day}天预测模型...")
            
            model = self.create_model(random_state_offset=day)
            model.fit(X_train_scaled, y_train.iloc[:, day-1])
            
            self.models[f'day_{day}'] = model
        
        print("模型训练完成！")
        
    def predict(self, input_features):
        """进行多步预测"""
        if self.scaler is not None:
            input_scaled = self.scaler.transform(input_features.reshape(1, -1))
        else:
            input_scaled = input_features.reshape(1, -1)
        
        predictions = []
        forecast_days = self.config.experiment_config.forecast_days
        
        for day in range(1, forecast_days + 1):
            model = self.models[f'day_{day}']
            pred = model.predict(input_scaled)[0]
            predictions.append(pred)
        
        return np.array(predictions)
    
    def evaluate_models(self):
        """评估模型性能"""
        print("\n=== 模型评估结果 ===")
        
        # 预测测试集
        test_predictions = []
        for i in range(len(self.X_test_scaled)):
            pred = self.predict(self.X_test_scaled[i])
            test_predictions.append(pred)
        
        test_predictions = np.array(test_predictions)
        
        # 计算每天的评估指标
        forecast_days = self.config.experiment_config.forecast_days
        daily_results = {}
        
        for day in range(forecast_days):
            y_true = self.y_test.iloc[:, day].values
            y_pred = test_predictions[:, day]
            
            mae = mean_absolute_error(y_true, y_pred)
            mse = mean_squared_error(y_true, y_pred)
            r2 = r2_score(y_true, y_pred)
            mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
            
            daily_results[f'day_{day+1}'] = {
                'MAE': mae,
                'MSE': mse,
                'R2': r2,
                'MAPE': mape,
                'relative_error': mae / np.mean(y_true) * 100
            }
            
            print(f"第{day+1}天预测 - MAE: {mae:.2f}, R²: {r2:.4f}, MAPE: {mape:.1f}%")
        
        # 整体评估
        all_true = self.y_test.values.flatten()
        all_pred = test_predictions.flatten()
        
        overall_mae = mean_absolute_error(all_true, all_pred)
        overall_r2 = r2_score(all_true, all_pred)
        overall_mape = np.mean(np.abs((all_true - all_pred) / all_true)) * 100
        
        overall_results = {
            'MAE': overall_mae,
            'R2': overall_r2,
            'MAPE': overall_mape
        }
        
        print(f"\n整体预测 - MAE: {overall_mae:.2f}, R²: {overall_r2:.4f}, MAPE: {overall_mape:.1f}%")
        
        # 保存结果
        self.results = {
            'daily_results': daily_results,
            'overall_results': overall_results,
            'predictions': test_predictions,
            'actuals': self.y_test.values
        }
        
        return self.results
    
    def plot_results(self):
        """绘制预测结果"""
        if not self.config.experiment_config.plot_results:
            return
            
        print("\n正在生成预测结果图表...")
        
        forecast_days = self.config.experiment_config.forecast_days
        predictions = self.results['predictions']
        actuals = self.results['actuals']
        
        # 创建子图
        fig, axes = plt.subplots(2, (forecast_days + 1) // 2, figsize=(15, 10))
        if forecast_days == 1:
            axes = [axes]
        else:
            axes = axes.flatten()
        
        # 绘制每天的预测结果
        for day in range(forecast_days):
            ax = axes[day]
            
            actual = actuals[:, day]
            pred = predictions[:, day]
            
            ax.scatter(actual, pred, alpha=0.6, s=20)
            ax.plot([actual.min(), actual.max()], [actual.min(), actual.max()], 'r--', lw=2)
            ax.set_xlabel('实际径流量')
            ax.set_ylabel('预测径流量')
            ax.set_title(f'第{day+1}天预测 (R²={self.results["daily_results"][f"day_{day+1}"]["R2"]:.3f})')
            ax.grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(forecast_days, len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.show()
    
    def generate_report(self):
        """生成预测报告"""
        if not self.config.experiment_config.generate_report:
            return
            
        print("\n正在生成预测报告...")
        
        report = {
            'experiment_info': {
                'forecast_days': self.config.experiment_config.forecast_days,
                'model_type': self.config.model_config.model_type,
                'n_estimators': self.config.model_config.n_estimators,
                'max_depth': self.config.model_config.max_depth,
                'feature_count': len(self.feature_columns),
                'data_points': len(self.data),
                'train_ratio': self.config.data_config.train_ratio
            },
            'results': self.results['daily_results'],
            'overall_performance': self.results['overall_results'],
            'feature_importance': self.get_feature_importance()
        }
        
        # 保存报告
        if self.config.experiment_config.save_model:
            os.makedirs(self.config.experiment_config.report_save_path, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"{self.config.experiment_config.report_save_path}/report_{self.config.experiment_config.forecast_days}day_{timestamp}.json"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"报告已保存到: {report_file}")
        
        return report
    
    def get_feature_importance(self):
        """获取特征重要性"""
        forecast_days = self.config.experiment_config.forecast_days
        importance_sum = np.zeros(len(self.feature_columns))
        
        for day in range(1, forecast_days + 1):
            model = self.models[f'day_{day}']
            if hasattr(model, 'feature_importances_'):
                importance_sum += model.feature_importances_
        
        avg_importance = importance_sum / forecast_days
        
        importance_df = pd.DataFrame({
            'feature': self.feature_columns,
            'importance': avg_importance
        }).sort_values('importance', ascending=False)
        
        return importance_df.head(10).to_dict('records')
    
    def demo_prediction(self):
        """演示预测功能"""
        print(f"\n=== {self.config.experiment_config.forecast_days}天预测演示 ===")
        
        # 使用最新数据进行演示
        latest_data = self.data.iloc[-1]
        input_features = latest_data[self.feature_columns].values
        
        print(f"基于 {latest_data['DATE'].strftime('%Y-%m-%d')} 的数据预测:")
        print(f"当日径流量: {latest_data['RUNOFF']:.2f}")
        
        # 进行预测
        predictions = self.predict(input_features)
        
        print(f"\n未来{self.config.experiment_config.forecast_days}天预测结果:")
        for i, pred in enumerate(predictions, 1):
            future_date = latest_data['DATE'] + pd.Timedelta(days=i)
            print(f"  第{i}天 ({future_date.strftime('%Y-%m-%d')}): {pred:.2f}")
        
        return predictions

def run_experiment(config_file: str):
    """运行实验"""
    print(f"=== 加载配置文件: {config_file} ===")
    
    # 加载配置
    config = HyperparameterConfig.load_from_file(config_file)
    
    # 创建预测器
    predictor = ConfigurableMultiStepPredictor(config)
    
    # 执行完整流程
    predictor.load_data()
    predictor.create_features()
    predictor.train_models()
    
    # 评估和报告
    results = predictor.evaluate_models()
    predictor.plot_results()
    report = predictor.generate_report()
    predictions = predictor.demo_prediction()
    
    return predictor, results, report, predictions

if __name__ == "__main__":
    # 首先创建配置文件
    from hyperparameters_config import create_default_configs
    create_default_configs()
    
    print("\n" + "="*50)
    print("开始3天预测实验")
    print("="*50)
    predictor_3day, results_3day, report_3day, pred_3day = run_experiment('config_3day.json')
    
    print("\n" + "="*50)
    print("开始5天预测实验")
    print("="*50)
    predictor_5day, results_5day, report_5day, pred_5day = run_experiment('config_5day.json')
