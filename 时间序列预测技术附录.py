"""
时间序列径流预测技术附录
包含详细的分析工具和可视化代码
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import GridSearchCV
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class TimeSeriesAnalyzer:
    """时间序列径流预测分析器"""
    
    def __init__(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.data_path = data_path
        self.data = None
        self.model = None
        self.scaler = None
        
    def load_data(self):
        """加载数据"""
        self.data = pd.read_csv(self.data_path)
        self.data['DATE'] = pd.to_datetime(self.data['DATE'])
        self.data = self.data.sort_values('DATE').reset_index(drop=True)
        print(f"数据加载完成，共{len(self.data)}条记录")
        
    def analyze_autocorrelation(self, max_lags=50):
        """分析径流的自相关性"""
        from statsmodels.tsa.stattools import acf
        
        # 计算自相关函数
        autocorr = acf(self.data['RUNOFF'].dropna(), nlags=max_lags)
        
        # 绘制自相关图
        plt.figure(figsize=(12, 6))
        plt.subplot(1, 2, 1)
        plt.plot(range(max_lags+1), autocorr, 'b-', linewidth=2)
        plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        plt.axhline(y=0.05, color='r', linestyle='--', alpha=0.5, label='5%显著性水平')
        plt.axhline(y=-0.05, color='r', linestyle='--', alpha=0.5)
        plt.xlabel('滞后天数')
        plt.ylabel('自相关系数')
        plt.title('径流自相关函数')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 重要滞后期分析
        plt.subplot(1, 2, 2)
        important_lags = [1, 2, 3, 7, 14, 30]
        important_autocorr = [autocorr[lag] for lag in important_lags]
        
        bars = plt.bar(range(len(important_lags)), important_autocorr, 
                      color=['red' if x > 0.1 else 'blue' for x in important_autocorr])
        plt.xticks(range(len(important_lags)), [f'{lag}天' for lag in important_lags])
        plt.ylabel('自相关系数')
        plt.title('关键滞后期自相关性')
        plt.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar, val) in enumerate(zip(bars, important_autocorr)):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{val:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.show()
        
        return autocorr
    
    def analyze_seasonal_patterns(self):
        """分析径流的季节性模式"""
        # 添加时间特征
        self.data['MONTH'] = self.data['DATE'].dt.month
        self.data['SEASON'] = self.data['MONTH'].map({
            12: '冬季', 1: '冬季', 2: '冬季',
            3: '春季', 4: '春季', 5: '春季',
            6: '夏季', 7: '夏季', 8: '夏季',
            9: '秋季', 10: '秋季', 11: '秋季'
        })
        
        plt.figure(figsize=(15, 10))
        
        # 月度径流分布
        plt.subplot(2, 3, 1)
        monthly_stats = self.data.groupby('MONTH')['RUNOFF'].agg(['mean', 'std', 'median'])
        plt.errorbar(monthly_stats.index, monthly_stats['mean'], 
                    yerr=monthly_stats['std'], fmt='o-', capsize=5)
        plt.xlabel('月份')
        plt.ylabel('径流量')
        plt.title('月度径流量变化')
        plt.grid(True, alpha=0.3)
        
        # 季节性箱线图
        plt.subplot(2, 3, 2)
        sns.boxplot(data=self.data, x='SEASON', y='RUNOFF')
        plt.title('季节性径流分布')
        plt.xticks(rotation=45)
        
        # 年际变化趋势
        plt.subplot(2, 3, 3)
        self.data['YEAR'] = self.data['DATE'].dt.year
        yearly_mean = self.data.groupby('YEAR')['RUNOFF'].mean()
        plt.plot(yearly_mean.index, yearly_mean.values, 'b-', linewidth=2)
        plt.xlabel('年份')
        plt.ylabel('年平均径流量')
        plt.title('年际径流变化趋势')
        plt.grid(True, alpha=0.3)
        
        # 径流量分布直方图
        plt.subplot(2, 3, 4)
        plt.hist(self.data['RUNOFF'], bins=50, alpha=0.7, edgecolor='black')
        plt.xlabel('径流量')
        plt.ylabel('频次')
        plt.title('径流量分布直方图')
        plt.grid(True, alpha=0.3)
        
        # 极值分析
        plt.subplot(2, 3, 5)
        # 计算年最大值和最小值
        yearly_extremes = self.data.groupby('YEAR')['RUNOFF'].agg(['min', 'max'])
        plt.plot(yearly_extremes.index, yearly_extremes['max'], 'r-', label='年最大值', linewidth=2)
        plt.plot(yearly_extremes.index, yearly_extremes['min'], 'b-', label='年最小值', linewidth=2)
        plt.xlabel('年份')
        plt.ylabel('径流量')
        plt.title('年极值变化')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 日内变化模式（以月为单位）
        plt.subplot(2, 3, 6)
        self.data['DAY_OF_MONTH'] = self.data['DATE'].dt.day
        daily_pattern = self.data.groupby('DAY_OF_MONTH')['RUNOFF'].mean()
        plt.plot(daily_pattern.index, daily_pattern.values, 'g-', linewidth=2)
        plt.xlabel('月内第几天')
        plt.ylabel('平均径流量')
        plt.title('月内日均径流模式')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        return monthly_stats, yearly_mean
    
    def create_advanced_features(self):
        """创建高级时间序列特征"""
        print("创建高级时间序列特征...")
        
        # 基础滞后特征
        lag_days = [1, 2, 3, 7, 14, 30]
        for lag in lag_days:
            self.data[f'RUNOFF_LAG_{lag}'] = self.data['RUNOFF'].shift(lag)
        
        # 移动平均特征
        windows = [3, 7, 14, 30]
        for window in windows:
            self.data[f'RUNOFF_MA_{window}'] = self.data['RUNOFF'].rolling(window=window).mean()
        
        # 移动标准差（波动性特征）
        for window in [7, 14, 30]:
            self.data[f'RUNOFF_STD_{window}'] = self.data['RUNOFF'].rolling(window=window).std()
        
        # 变化率特征
        for lag in [1, 7, 30]:
            self.data[f'RUNOFF_CHANGE_{lag}'] = self.data['RUNOFF'] - self.data[f'RUNOFF_LAG_{lag}']
            self.data[f'RUNOFF_PCT_CHANGE_{lag}'] = self.data['RUNOFF'].pct_change(periods=lag)
        
        # 极值特征
        for window in [7, 14, 30]:
            self.data[f'RUNOFF_MAX_{window}'] = self.data['RUNOFF'].rolling(window=window).max()
            self.data[f'RUNOFF_MIN_{window}'] = self.data['RUNOFF'].rolling(window=window).min()
        
        # 时间特征
        self.data['MONTH'] = self.data['DATE'].dt.month
        self.data['DAY_OF_YEAR'] = self.data['DATE'].dt.dayofyear
        self.data['WEEK_OF_YEAR'] = self.data['DATE'].dt.isocalendar().week
        
        # 周期性特征（正弦余弦编码）
        self.data['MONTH_SIN'] = np.sin(2 * np.pi * self.data['MONTH'] / 12)
        self.data['MONTH_COS'] = np.cos(2 * np.pi * self.data['MONTH'] / 12)
        self.data['DAY_SIN'] = np.sin(2 * np.pi * self.data['DAY_OF_YEAR'] / 365)
        self.data['DAY_COS'] = np.cos(2 * np.pi * self.data['DAY_OF_YEAR'] / 365)
        
        print(f"特征工程完成，总特征数：{len(self.data.columns)}")
        
    def feature_importance_analysis(self):
        """特征重要性分析"""
        # 准备数据
        feature_columns = [col for col in self.data.columns 
                          if col.startswith('RUNOFF_') and col != 'RUNOFF'] + \
                         ['MONTH', 'DAY_OF_YEAR', 'WEEK_OF_YEAR', 
                          'MONTH_SIN', 'MONTH_COS', 'DAY_SIN', 'DAY_COS']
        
        # 删除缺失值
        data_clean = self.data.dropna()
        X = data_clean[feature_columns]
        y = data_clean['RUNOFF']
        
        # 训练随机森林
        rf = RandomForestRegressor(n_estimators=100, random_state=42)
        rf.fit(X, y)
        
        # 获取特征重要性
        importance_df = pd.DataFrame({
            'feature': feature_columns,
            'importance': rf.feature_importances_
        }).sort_values('importance', ascending=False)
        
        # 可视化特征重要性
        plt.figure(figsize=(12, 8))
        top_features = importance_df.head(15)
        
        bars = plt.barh(range(len(top_features)), top_features['importance'])
        plt.yticks(range(len(top_features)), top_features['feature'])
        plt.xlabel('特征重要性')
        plt.title('Top 15 特征重要性排序')
        plt.gca().invert_yaxis()
        
        # 添加数值标签
        for i, (bar, val) in enumerate(zip(bars, top_features['importance'])):
            plt.text(bar.get_width() + 0.001, bar.get_y() + bar.get_height()/2, 
                    f'{val:.3f}', ha='left', va='center')
        
        plt.tight_layout()
        plt.show()
        
        return importance_df
    
    def prediction_interval_analysis(self, confidence_level=0.95):
        """预测区间分析"""
        from sklearn.ensemble import RandomForestRegressor
        
        # 准备数据
        feature_columns = [f'RUNOFF_LAG_{lag}' for lag in [1,2,3,7,14,30]] + \
                         [f'RUNOFF_MA_{window}' for window in [3,7,14,30]] + \
                         ['MONTH', 'DAY_OF_YEAR']
        
        data_clean = self.data.dropna()
        X = data_clean[feature_columns]
        y = data_clean['RUNOFF']
        
        # 时间序列分割
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        # 标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # 训练多个模型（Bootstrap）
        n_models = 100
        predictions = []
        
        for i in range(n_models):
            # Bootstrap采样
            indices = np.random.choice(len(X_train_scaled), size=len(X_train_scaled), replace=True)
            X_boot = X_train_scaled[indices]
            y_boot = y_train.iloc[indices]
            
            # 训练模型
            rf = RandomForestRegressor(n_estimators=50, random_state=i)
            rf.fit(X_boot, y_boot)
            
            # 预测
            pred = rf.predict(X_test_scaled)
            predictions.append(pred)
        
        # 计算预测区间
        predictions = np.array(predictions)
        pred_mean = np.mean(predictions, axis=0)
        pred_std = np.std(predictions, axis=0)
        
        alpha = 1 - confidence_level
        lower_bound = np.percentile(predictions, 100 * alpha/2, axis=0)
        upper_bound = np.percentile(predictions, 100 * (1 - alpha/2), axis=0)
        
        # 可视化预测区间
        plt.figure(figsize=(15, 8))
        
        # 选择最后500个点进行可视化
        n_points = min(500, len(y_test))
        x_axis = range(n_points)
        
        plt.fill_between(x_axis, 
                        lower_bound[-n_points:], 
                        upper_bound[-n_points:], 
                        alpha=0.3, color='gray', label=f'{confidence_level*100}%预测区间')
        
        plt.plot(x_axis, y_test.iloc[-n_points:], 'b-', label='实际值', linewidth=2)
        plt.plot(x_axis, pred_mean[-n_points:], 'r-', label='预测均值', linewidth=2)
        
        plt.xlabel('时间点')
        plt.ylabel('径流量')
        plt.title(f'径流预测区间分析（置信度{confidence_level*100}%）')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()
        
        # 计算区间覆盖率
        coverage = np.mean((y_test >= lower_bound) & (y_test <= upper_bound))
        print(f"预测区间覆盖率: {coverage:.3f}")
        print(f"理论覆盖率: {confidence_level:.3f}")
        
        return pred_mean, lower_bound, upper_bound, coverage

# 使用示例
if __name__ == "__main__":
    # 创建分析器
    analyzer = TimeSeriesAnalyzer()
    
    # 加载数据
    analyzer.load_data()
    
    print("=== 1. 自相关性分析 ===")
    autocorr = analyzer.analyze_autocorrelation()
    
    print("\n=== 2. 季节性模式分析 ===")
    monthly_stats, yearly_mean = analyzer.analyze_seasonal_patterns()
    
    print("\n=== 3. 创建高级特征 ===")
    analyzer.create_advanced_features()
    
    print("\n=== 4. 特征重要性分析 ===")
    importance_df = analyzer.feature_importance_analysis()
    print("Top 10 重要特征:")
    print(importance_df.head(10))
    
    print("\n=== 5. 预测区间分析 ===")
    pred_mean, lower_bound, upper_bound, coverage = analyzer.prediction_interval_analysis()
    
    print("\n分析完成！")
