"""
基于已知有效配置的改进第5天优化器
目标：第5天R²达到0.7以上
策略：
1. 基于PSO优化结果的有效配置
2. 专门的第5天损失函数
3. 更精细的超参数调整
4. 集成学习
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

def nash_sutcliffe_efficiency(y_true, y_pred):
    """计算纳什效率系数"""
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    ss_res = np.sum((y_true - y_pred) ** 2)
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    nse = 1 - (ss_res / ss_tot)
    return nse

class ImprovedCALFModel(nn.Module):
    """改进的CALF模型"""
    
    def __init__(self, seq_len=48, pred_len=7, d_model=256, n_heads=4, n_layers=2):
        super(ImprovedCALFModel, self).__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        
        # 输入投影层
        self.input_projection = nn.Linear(1, d_model)
        
        # 位置编码
        self.pos_embedding = nn.Parameter(torch.randn(seq_len, d_model))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 2,
            dropout=0.1,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 预测头
        self.prediction_head = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, pred_len)
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(d_model)
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """初始化模型权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)
        
        # 添加位置编码
        x = x + self.pos_embedding.unsqueeze(0).expand(batch_size, -1, -1)
        
        # 层归一化
        x = self.layer_norm(x)
        
        # Transformer编码
        x = self.transformer_encoder(x)
        
        # 全局平均池化
        x = x.mean(dim=1)
        
        # 预测
        output = self.prediction_head(x)
        
        return output

class Day5SpecialLoss(nn.Module):
    """专门针对第5天的特殊损失函数"""
    
    def __init__(self, day5_weight=10.0, r2_weight=1.0):
        super(Day5SpecialLoss, self).__init__()
        self.day5_weight = day5_weight
        self.r2_weight = r2_weight
        self.mse_loss = nn.MSELoss()
        
    def forward(self, predictions, targets):
        # 基础MSE损失
        mse_loss = self.mse_loss(predictions, targets)
        
        # 第5天专门损失
        day5_pred = predictions[:, 4]
        day5_targets = targets[:, 4]
        day5_mse = self.mse_loss(day5_pred, day5_targets)
        
        # R²相关的损失（最大化R²等价于最小化残差）
        day5_mean = torch.mean(day5_targets)
        ss_res = torch.sum((day5_targets - day5_pred) ** 2)
        ss_tot = torch.sum((day5_targets - day5_mean) ** 2)
        r2_loss = ss_res / (ss_tot + 1e-8)  # 添加小常数避免除零
        
        # 组合损失
        total_loss = mse_loss + self.day5_weight * day5_mse + self.r2_weight * r2_loss
        
        return total_loss

class ImprovedDay5Optimizer:
    """改进的第5天优化器"""
    
    def __init__(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 加载和准备数据
        self.prepare_data(data_path)
        
    def prepare_data(self, data_path):
        """准备数据"""
        print("正在加载和准备径流数据...")
        
        # 加载数据
        data = pd.read_csv(data_path)
        data['DATE'] = pd.to_datetime(data['DATE'])
        data = data.sort_values('DATE').reset_index(drop=True)
        data = data.dropna()
        
        # 只使用径流数据
        self.runoff_data = data['RUNOFF'].values
        self.scaler = StandardScaler()
        
        print(f"数据加载完成：{len(data)}条记录")
        
    def create_time_series_data(self, seq_len, pred_len=7):
        """创建时间序列数据"""
        X, y = [], []
        
        for i in range(len(self.runoff_data) - seq_len - pred_len + 1):
            X.append(self.runoff_data[i:i + seq_len])
            y.append(self.runoff_data[i + seq_len:i + seq_len + pred_len])
        
        X = np.array(X)
        y = np.array(y)
        
        # 数据标准化
        X_reshaped = X.reshape(-1, 1)
        y_reshaped = y.reshape(-1, 1)
        
        all_data = np.concatenate([X_reshaped, y_reshaped], axis=0)
        self.scaler.fit(all_data)
        
        X_scaled = self.scaler.transform(X_reshaped).reshape(X.shape)
        y_scaled = self.scaler.transform(y_reshaped).reshape(y.shape)
        
        # 按7:3划分数据集
        split_idx = int(len(X) * 0.7)
        
        X_train = torch.FloatTensor(X_scaled[:split_idx]).unsqueeze(-1)
        X_test = torch.FloatTensor(X_scaled[split_idx:]).unsqueeze(-1)
        y_train = torch.FloatTensor(y_scaled[:split_idx])
        y_test = torch.FloatTensor(y_scaled[split_idx:])
        
        return X_train, X_test, y_train, y_test
    
    def train_single_model(self, config):
        """训练单个模型"""
        print(f"训练配置: {config}")
        
        # 创建数据
        X_train, X_test, y_train, y_test = self.create_time_series_data(
            seq_len=config['seq_len']
        )
        
        # 创建模型
        model = ImprovedCALFModel(
            seq_len=config['seq_len'],
            pred_len=7,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers']
        ).to(self.device)
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"模型参数数量: {total_params:,}")
        
        # 创建数据加载器
        train_dataset = TensorDataset(X_train, y_train)
        train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)
        
        # 优化器和损失函数
        optimizer = torch.optim.AdamW(model.parameters(), lr=config['learning_rate'], weight_decay=0.01)
        criterion = Day5SpecialLoss(day5_weight=config.get('day5_weight', 10.0))
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=config['epochs'], eta_min=1e-6)
        
        # 训练
        model.train()
        best_day5_r2 = -float('inf')
        patience = 20
        patience_counter = 0
        
        for epoch in range(config['epochs']):
            epoch_loss = 0.0
            
            for batch_x, batch_y in train_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                predictions = model(batch_x)
                loss = criterion(predictions, batch_y)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                
                epoch_loss += loss.item()
            
            scheduler.step()
            
            # 每5轮评估一次
            if (epoch + 1) % 5 == 0:
                model.eval()
                with torch.no_grad():
                    X_test_device = X_test.to(self.device)
                    predictions = model(X_test_device).cpu().numpy()
                    
                    # 反标准化
                    y_test_original = self.scaler.inverse_transform(
                        y_test.numpy().reshape(-1, 1)
                    ).reshape(y_test.shape)
                    
                    y_pred_original = self.scaler.inverse_transform(
                        predictions.reshape(-1, 1)
                    ).reshape(predictions.shape)
                    
                    # 计算第5天R²
                    day5_r2 = r2_score(y_test_original[:, 4], y_pred_original[:, 4])
                    
                    if day5_r2 > best_day5_r2:
                        best_day5_r2 = day5_r2
                        patience_counter = 0
                        torch.save(model.state_dict(), f'best_improved_model_{config["name"]}.pth')
                    else:
                        patience_counter += 1
                    
                    print(f"Epoch {epoch+1}: Loss = {epoch_loss/len(train_loader):.6f}, 第5天R² = {day5_r2:.4f}")
                    
                    if day5_r2 >= 0.7:
                        print(f"🎯 达到目标！第5天R² = {day5_r2:.4f} >= 0.7")
                        break
                
                model.train()
                
                if patience_counter >= patience:
                    print(f"早停触发，最佳第5天R² = {best_day5_r2:.4f}")
                    break
        
        # 加载最佳模型并最终评估
        model.load_state_dict(torch.load(f'best_improved_model_{config["name"]}.pth'))
        model.eval()
        
        with torch.no_grad():
            X_test_device = X_test.to(self.device)
            predictions = model(X_test_device).cpu().numpy()
            
            # 反标准化
            y_test_original = self.scaler.inverse_transform(
                y_test.numpy().reshape(-1, 1)
            ).reshape(y_test.shape)
            
            y_pred_original = self.scaler.inverse_transform(
                predictions.reshape(-1, 1)
            ).reshape(predictions.shape)
        
        # 计算所有天数的R²和NSE
        daily_r2 = []
        daily_nse = []
        for day in range(7):
            r2 = r2_score(y_test_original[:, day], y_pred_original[:, day])
            nse = nash_sutcliffe_efficiency(y_test_original[:, day], y_pred_original[:, day])
            daily_r2.append(r2)
            daily_nse.append(nse)
        
        # 计算整体NSE
        overall_nse = nash_sutcliffe_efficiency(
            y_test_original.flatten(), 
            y_pred_original.flatten()
        )
        
        return daily_r2, daily_nse, overall_nse, model, y_pred_original
    
    def optimize_for_day5(self):
        """优化第5天性能"""
        print("开始改进的第5天专门优化...")
        
        # 基于已知有效配置的改进版本
        configs = [
            {
                'name': 'config1',
                'seq_len': 48, 'd_model': 256, 'n_heads': 4, 'n_layers': 2,
                'learning_rate': 0.002, 'batch_size': 32, 'epochs': 100, 'day5_weight': 8.0
            },
            {
                'name': 'config2', 
                'seq_len': 64, 'd_model': 320, 'n_heads': 4, 'n_layers': 3,
                'learning_rate': 0.0015, 'batch_size': 28, 'epochs': 120, 'day5_weight': 10.0
            },
            {
                'name': 'config3',
                'seq_len': 80, 'd_model': 384, 'n_heads': 6, 'n_layers': 3,
                'learning_rate': 0.001, 'batch_size': 24, 'epochs': 150, 'day5_weight': 12.0
            },
            {
                'name': 'config4',
                'seq_len': 96, 'd_model': 512, 'n_heads': 8, 'n_layers': 4,
                'learning_rate': 0.0008, 'batch_size': 20, 'epochs': 180, 'day5_weight': 15.0
            }
        ]
        
        best_day5_r2 = -float('inf')
        best_config = None
        best_results = None
        all_predictions = []
        
        for i, config in enumerate(configs):
            print(f"\n=== 测试配置 {i+1}/{len(configs)} ===")
            print(f"参数: seq_len={config['seq_len']}, d_model={config['d_model']}, day5_weight={config['day5_weight']}")
            
            try:
                daily_r2, daily_nse, overall_nse, model, predictions = self.train_single_model(config)
                all_predictions.append(predictions)
                
                print(f"\n结果:")
                for day, (r2, nse) in enumerate(zip(daily_r2, daily_nse), 1):
                    status = "✅" if r2 >= 0.7 else "⚠️" if r2 >= 0.6 else "❌"
                    print(f"  第{day}天: R² = {r2:.4f}, NSE = {nse:.4f} {status}")
                print(f"  整体NSE: {overall_nse:.4f}")
                
                if daily_r2[4] > best_day5_r2:  # 第5天（索引4）
                    best_day5_r2 = daily_r2[4]
                    best_config = config
                    best_results = (daily_r2, daily_nse, overall_nse, model)
                    print(f"  *** 新的最佳第5天R²: {best_day5_r2:.4f} ***")
                
                if daily_r2[4] >= 0.7:
                    print(f"  🎯 达到目标！第5天R² = {daily_r2[4]:.4f} >= 0.7")
                    break
                    
            except Exception as e:
                print(f"  配置{i+1}训练失败: {e}")
                continue
        
        # 如果没有达到目标，尝试集成学习
        if best_day5_r2 < 0.7 and len(all_predictions) > 1:
            print(f"\n=== 尝试集成学习 ===")
            ensemble_pred = np.mean(all_predictions, axis=0)
            
            # 重新计算评估指标
            # 这里需要重新获取测试数据
            X_train, X_test, y_train, y_test = self.create_time_series_data(seq_len=48)
            y_test_original = self.scaler.inverse_transform(
                y_test.numpy().reshape(-1, 1)
            ).reshape(y_test.shape)
            
            ensemble_original = self.scaler.inverse_transform(
                ensemble_pred.reshape(-1, 1)
            ).reshape(ensemble_pred.shape)
            
            ensemble_daily_r2 = []
            for day in range(7):
                r2 = r2_score(y_test_original[:, day], ensemble_original[:, day])
                ensemble_daily_r2.append(r2)
            
            print(f"集成学习结果:")
            for day, r2 in enumerate(ensemble_daily_r2, 1):
                status = "✅" if r2 >= 0.7 else "⚠️" if r2 >= 0.6 else "❌"
                print(f"  第{day}天: R² = {r2:.4f} {status}")
            
            if ensemble_daily_r2[4] > best_day5_r2:
                best_day5_r2 = ensemble_daily_r2[4]
                print(f"  *** 集成学习提升第5天R²: {best_day5_r2:.4f} ***")
        
        return best_config, best_results, best_day5_r2

def main():
    """主函数"""
    print("=== 改进的第5天R²专门优化系统 ===")
    print("目标：第5天R² >= 0.7")
    print("策略：基于有效配置 + 专门损失函数 + 集成学习")
    
    optimizer = ImprovedDay5Optimizer()
    
    best_config, best_results, best_day5_r2 = optimizer.optimize_for_day5()
    
    print("\n" + "="*60)
    print("最终优化结果")
    print("="*60)
    
    if best_results:
        daily_r2, daily_nse, overall_nse, model = best_results
        
        print(f"最佳配置: {best_config}")
        print(f"第5天R²: {best_day5_r2:.4f}")
        print(f"目标达成: {'✅ 是' if best_day5_r2 >= 0.7 else '❌ 否'}")
        
        print(f"\n所有天数详细结果:")
        for day, (r2, nse) in enumerate(zip(daily_r2, daily_nse), 1):
            status = "✅" if r2 >= 0.7 else "⚠️" if r2 >= 0.6 else "❌"
            print(f"  第{day}天: R² = {r2:.4f}, NSE = {nse:.4f} {status}")
        
        print(f"\n整体NSE: {overall_nse:.4f}")
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"最终模型参数数量: {total_params:,}")
    else:
        print("未找到有效配置")
        print(f"最佳第5天R²: {best_day5_r2:.4f}")
    
    return optimizer, best_config, best_day5_r2

if __name__ == "__main__":
    optimizer, best_config, best_day5_r2 = main()
