"""
快速CALF模型测试
使用较小的模型和较少的训练轮数进行快速验证
7:3数据分割
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

class QuickCALFModel(nn.Module):
    """快速CALF模型，用于测试"""
    
    def __init__(self, seq_len=48, pred_len=7, d_model=256, n_heads=4, n_layers=2):
        super(QuickCALFModel, self).__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        
        # 输入投影层
        self.input_projection = nn.Linear(1, d_model)
        
        # 位置编码
        self.pos_embedding = nn.Parameter(torch.randn(seq_len, d_model))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 2,
            dropout=0.1,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, pred_len)
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        # x shape: [batch_size, seq_len, 1]
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)  # [batch_size, seq_len, d_model]
        
        # 添加位置编码
        x = x + self.pos_embedding.unsqueeze(0).expand(batch_size, -1, -1)
        
        # 层归一化
        x = self.layer_norm(x)
        
        # Transformer编码
        x = self.transformer_encoder(x)  # [batch_size, seq_len, d_model]
        
        # 全局平均池化
        x = x.mean(dim=1)  # [batch_size, d_model]
        
        # 输出投影
        output = self.output_projection(x)  # [batch_size, pred_len]
        
        return output

class QuickCALFPredictor:
    """快速CALF径流预测器"""
    
    def __init__(self, seq_len=48, pred_len=7):
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        self.data = None
        self.model = QuickCALFModel(seq_len, pred_len, d_model=256, n_heads=4, n_layers=2)
        self.scaler = StandardScaler()
        self.results = {}
        
    def load_and_prepare_data(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        """加载和准备数据"""
        print("正在加载和准备径流数据...")
        
        # 加载数据
        self.data = pd.read_csv(data_path)
        self.data['DATE'] = pd.to_datetime(self.data['DATE'])
        self.data = self.data.sort_values('DATE').reset_index(drop=True)
        self.data = self.data.dropna()
        
        # 只使用径流数据
        runoff_data = self.data['RUNOFF'].values
        
        # 创建时间序列样本
        X, y = [], []
        
        for i in range(len(runoff_data) - self.seq_len - self.pred_len + 1):
            X.append(runoff_data[i:i + self.seq_len])
            y.append(runoff_data[i + self.seq_len:i + self.seq_len + self.pred_len])
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"生成样本数量: {len(X)}")
        print(f"输入序列长度: {self.seq_len}, 预测序列长度: {self.pred_len}")
        
        # 数据标准化
        X_reshaped = X.reshape(-1, 1)
        y_reshaped = y.reshape(-1, 1)
        
        all_data = np.concatenate([X_reshaped, y_reshaped], axis=0)
        self.scaler.fit(all_data)
        
        X_scaled = self.scaler.transform(X_reshaped).reshape(X.shape)
        y_scaled = self.scaler.transform(y_reshaped).reshape(y.shape)
        
        # 按7:3划分数据集
        split_idx = int(len(X) * 0.7)
        
        self.X_train = torch.FloatTensor(X_scaled[:split_idx]).unsqueeze(-1)
        self.X_test = torch.FloatTensor(X_scaled[split_idx:]).unsqueeze(-1)
        self.y_train = torch.FloatTensor(y_scaled[:split_idx])
        self.y_test = torch.FloatTensor(y_scaled[split_idx:])
        
        print(f"训练集大小: {len(self.X_train)} (70%)")
        print(f"测试集大小: {len(self.X_test)} (30%)")
        
    def train_model(self, epochs=20, batch_size=64, learning_rate=0.001):
        """训练模型"""
        print("开始训练快速CALF模型...")
        
        self.model.to(self.device)
        
        # 创建数据加载器
        train_dataset = TensorDataset(self.X_train, self.y_train)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        
        # 优化器和损失函数
        optimizer = torch.optim.Adam(self.model.parameters(), lr=learning_rate)
        criterion = nn.MSELoss()
        
        # 计算模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        print(f"模型参数数量: {total_params:,}")
        
        # 训练循环
        self.model.train()
        
        for epoch in range(epochs):
            epoch_loss = 0.0
            batch_count = 0
            
            for batch_x, batch_y in train_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_x)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                epoch_loss += loss.item()
                batch_count += 1
            
            avg_loss = epoch_loss / batch_count
            
            if (epoch + 1) % 5 == 0:
                print(f"Epoch [{epoch+1}/{epochs}], Loss: {avg_loss:.6f}")
        
        print("模型训练完成！")
        
    def evaluate_model(self):
        """评估模型"""
        print("正在评估模型...")
        
        self.model.eval()
        
        with torch.no_grad():
            # 测试集预测
            X_test_device = self.X_test.to(self.device)
            y_pred_scaled = self.model(X_test_device).cpu().numpy()
            
            # 反标准化
            y_test_original = self.scaler.inverse_transform(
                self.y_test.numpy().reshape(-1, 1)
            ).reshape(self.y_test.shape)
            
            y_pred_original = self.scaler.inverse_transform(
                y_pred_scaled.reshape(-1, 1)
            ).reshape(y_pred_scaled.shape)
        
        # 计算评估指标
        results = {}
        
        print(f"\n=== {self.pred_len}天径流预测结果 ===")
        
        # 逐天评估
        for day in range(self.pred_len):
            y_true_day = y_test_original[:, day]
            y_pred_day = y_pred_original[:, day]
            
            mae = mean_absolute_error(y_true_day, y_pred_day)
            mse = mean_squared_error(y_true_day, y_pred_day)
            r2 = r2_score(y_true_day, y_pred_day)
            mape = np.mean(np.abs((y_true_day - y_pred_day) / y_true_day)) * 100
            
            results[f'day_{day+1}'] = {
                'MAE': mae,
                'MSE': mse,
                'R2': r2,
                'MAPE': mape
            }
            
            print(f"第{day+1}天预测 - MAE: {mae:.2f}, R²: {r2:.4f}, MAPE: {mape:.1f}%")
        
        # 整体评估
        y_true_all = y_test_original.flatten()
        y_pred_all = y_pred_original.flatten()
        
        overall_mae = mean_absolute_error(y_true_all, y_pred_all)
        overall_mse = mean_squared_error(y_true_all, y_pred_all)
        overall_r2 = r2_score(y_true_all, y_pred_all)
        overall_mape = np.mean(np.abs((y_true_all - y_pred_all) / y_true_all)) * 100
        
        results['overall'] = {
            'MAE': overall_mae,
            'MSE': overall_mse,
            'R2': overall_r2,
            'MAPE': overall_mape
        }
        
        print(f"\n整体预测 - MAE: {overall_mae:.2f}, R²: {overall_r2:.4f}, MAPE: {overall_mape:.1f}%")
        
        # 保存结果
        self.results = {
            'daily_results': results,
            'predictions': y_pred_original,
            'actuals': y_test_original
        }
        
        return results
        
    def demo_prediction(self):
        """演示预测功能"""
        print(f"\n=== 快速CALF模型{self.pred_len}天径流预测演示 ===")
        
        # 使用最后一个测试样本进行演示
        demo_input = self.X_test[-1:].to(self.device)
        
        self.model.eval()
        with torch.no_grad():
            demo_pred_scaled = self.model(demo_input).cpu().numpy()
            demo_pred = self.scaler.inverse_transform(
                demo_pred_scaled.reshape(-1, 1)
            ).reshape(demo_pred_scaled.shape)
        
        print(f"输入序列长度: {self.seq_len}天")
        print(f"预测序列长度: {self.pred_len}天")
        print(f"\n未来{self.pred_len}天径流预测:")
        
        for i, pred in enumerate(demo_pred[0], 1):
            print(f"  第{i}天: {pred:.2f}")
        
        return demo_pred[0]

def main():
    """主函数"""
    print("=== 快速CALF径流预测系统测试 ===")
    print("模型配置: 轻量级Transformer")
    print("数据分割: 70%训练集, 30%测试集")
    print("训练轮数: 20轮 (快速测试)")
    
    # 创建预测器
    predictor = QuickCALFPredictor(
        seq_len=48,      # 输入序列长度：48天
        pred_len=7       # 预测序列长度：7天
    )
    
    # 执行完整流程
    predictor.load_and_prepare_data()
    predictor.train_model(epochs=20, batch_size=64, learning_rate=0.001)
    results = predictor.evaluate_model()
    demo_pred = predictor.demo_prediction()
    
    print("\n=== 实验完成 ===")
    print("模型架构: 快速CALF (轻量级Transformer)")
    print("数据分割: 7:3")
    print("预测任务: 7天径流预测")
    print("训练状态: 完成")
    
    return predictor, results, demo_pred

if __name__ == "__main__":
    predictor, results, demo_pred = main()
