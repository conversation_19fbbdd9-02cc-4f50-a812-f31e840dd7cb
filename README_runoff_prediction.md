# 长北地区径流预测模型

基于1971-2017年日气象数据的径流预测系统

## 项目概述

本项目使用长北地区1971-2017年的日气象数据来预测径流量，提供了两种不同的预测方案：

1. **方案1：气象特征预测** - 使用气象变量（温度、湿度、气压等）预测径流
2. **方案2：时间序列预测** - 使用历史径流数据预测未来径流

## 数据集信息

- **文件名**: `1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv`
- **数据量**: 17,167条记录（47年日数据）
- **特征**: 11个变量
  - DATE: 日期
  - RUNOFF: 径流量（目标变量）
  - TEMP: 温度
  - DEWP: 露点温度
  - SLP: 海平面气压
  - VISIB: 能见度
  - WDSP: 风速
  - MXSPD: 最大风速
  - MAX: 最高温度
  - MIN: 最低温度
  - PRCP: 降水量

## 模型性能

### 方案1：气象特征预测
- **训练集性能**: R² = 0.7011, MAE = 710.47
- **测试集性能**: R² = 0.5647, MAE = 848.73
- **特征数量**: 13个（包含日期特征）

### 方案2：时间序列预测
- **训练集性能**: R² = 0.9986, MAE = 43.83
- **测试集性能**: R² = 0.9895, MAE = 83.75
- **特征数量**: 12个（滞后特征和移动平均）

## 文件结构

```
├── 1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv  # 原始数据
├── runoff_prediction_config.py                          # 主要配置和模型类
├── run_prediction.py                                    # 简化运行脚本
├── requirements.txt                                     # 依赖包
└── README_runoff_prediction.md                         # 说明文档
```

## 使用方法

### 方法1：直接运行完整测试
```bash
python runoff_prediction_config.py
```
这将运行两种方案并显示结果图表。

### 方法2：交互式运行
```bash
python run_prediction.py
```
根据提示选择预测方案。

### 方法3：在代码中使用
```python
from runoff_prediction_config import RunoffPredictor

# 创建预测器
predictor = RunoffPredictor()

# 选择方案并训练
predictor.load_and_preprocess_data(method='weather_features')  # 或 'time_series'
predictor.train_model(model_type='random_forest')

# 评估模型
results = predictor.evaluate_model()
predictor.plot_results(results)
```

## 依赖包

确保安装以下Python包：
- pandas >= 1.4.2
- numpy
- scikit-learn >= 1.0.2
- matplotlib >= 3.8.3
- seaborn

## 模型特点

### 方案1：气象特征预测
- **优点**: 可以基于天气预报进行径流预测
- **缺点**: 预测精度相对较低
- **适用场景**: 长期预测、基于气象预报的径流预测

### 方案2：时间序列预测
- **优点**: 预测精度很高（R² > 0.98）
- **缺点**: 需要历史径流数据，预测范围有限
- **适用场景**: 短期预测、连续监测场景

## 结果解释

- **R²**: 决定系数，越接近1表示模型解释能力越强
- **MAE**: 平均绝对误差，数值越小表示预测越准确
- **MSE**: 均方误差，用于评估预测的整体偏差

## 注意事项

1. 数据中有少量缺失值已被自动处理
2. 时间序列方案使用时间顺序分割数据（前80%训练，后20%测试）
3. 气象特征方案使用随机分割数据
4. 模型已进行特征标准化处理

## 扩展建议

1. 可以尝试其他机器学习算法（如XGBoost、LSTM等）
2. 可以添加更多特征工程（如季节性特征、极值特征等）
3. 可以进行超参数调优以提升模型性能
4. 可以添加模型集成方法提高预测稳定性
