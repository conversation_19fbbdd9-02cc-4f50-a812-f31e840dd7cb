"""
完整CALF模型径流预测器
使用NSE和R²作为主要评价指标
7:3数据分割
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
import os
import argparse
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

try:
    from models.CALF import Model
    CALF_AVAILABLE = True
except Exception as e:
    print(f"原始CALF模型导入失败: {e}")
    CALF_AVAILABLE = False

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def nash_sutcliffe_efficiency(y_true, y_pred):
    """
    计算纳什效率系数 (Nash-Sutcliffe Efficiency, NSE)
    NSE = 1 - (SS_res / SS_tot)
    其中 SS_res = Σ(y_true - y_pred)²
         SS_tot = Σ(y_true - mean(y_true))²
    
    NSE范围: (-∞, 1]
    NSE = 1: 完美预测
    NSE = 0: 预测效果等同于使用观测值的平均值
    NSE < 0: 预测效果比使用平均值还差
    """
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    
    # 计算残差平方和
    ss_res = np.sum((y_true - y_pred) ** 2)
    
    # 计算总平方和
    ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
    
    # 计算NSE
    nse = 1 - (ss_res / ss_tot)
    
    return nse

class FullCALFModel(nn.Module):
    """完整的CALF模型实现"""
    
    def __init__(self, seq_len=96, pred_len=7, d_model=768, n_heads=12, n_layers=6):
        super(FullCALFModel, self).__init__()
        
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.d_model = d_model
        
        # 输入投影层
        self.input_projection = nn.Linear(1, d_model)
        
        # 位置编码
        self.pos_embedding = nn.Parameter(torch.randn(seq_len, d_model))
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=0.1,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 预测头
        self.prediction_head = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, d_model // 4),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 4, pred_len)
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(0.1)
        
        # 初始化权重
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """初始化模型权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.ones_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        # x shape: [batch_size, seq_len, 1]
        batch_size, seq_len, _ = x.shape
        
        # 输入投影
        x = self.input_projection(x)  # [batch_size, seq_len, d_model]
        
        # 添加位置编码
        x = x + self.pos_embedding.unsqueeze(0).expand(batch_size, -1, -1)
        
        # 层归一化和dropout
        x = self.layer_norm(x)
        x = self.dropout(x)
        
        # Transformer编码
        x = self.transformer_encoder(x)  # [batch_size, seq_len, d_model]
        
        # 全局平均池化
        x = x.mean(dim=1)  # [batch_size, d_model]
        
        # 预测
        output = self.prediction_head(x)  # [batch_size, pred_len]
        
        return output

class FullCALFPredictor:
    """完整CALF径流预测器"""
    
    def __init__(self, seq_len=96, pred_len=7, d_model=768, n_heads=12, n_layers=6):
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        self.data = None
        self.model = FullCALFModel(seq_len, pred_len, d_model, n_heads, n_layers)
        self.scaler = StandardScaler()
        self.results = {}
        
    def load_data(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        """加载径流数据"""
        print("正在加载径流数据...")
        self.data = pd.read_csv(data_path)
        self.data['DATE'] = pd.to_datetime(self.data['DATE'])
        self.data = self.data.sort_values('DATE').reset_index(drop=True)
        self.data = self.data.dropna()
        print(f"数据加载完成：{len(self.data)}条记录")
        
    def prepare_time_series_data(self):
        """准备时间序列数据"""
        print("正在准备时间序列数据...")
        
        # 只使用径流数据进行时间序列预测
        runoff_data = self.data['RUNOFF'].values
        
        # 创建时间序列样本
        X, y = [], []
        
        for i in range(len(runoff_data) - self.seq_len - self.pred_len + 1):
            # 输入序列：过去seq_len天的径流数据
            X.append(runoff_data[i:i + self.seq_len])
            # 输出序列：未来pred_len天的径流数据
            y.append(runoff_data[i + self.seq_len:i + self.seq_len + self.pred_len])
        
        X = np.array(X)
        y = np.array(y)
        
        print(f"生成样本数量: {len(X)}")
        print(f"输入序列长度: {self.seq_len}, 预测序列长度: {self.pred_len}")
        
        # 数据标准化
        X_reshaped = X.reshape(-1, 1)
        y_reshaped = y.reshape(-1, 1)
        
        # 拟合标准化器
        all_data = np.concatenate([X_reshaped, y_reshaped], axis=0)
        self.scaler.fit(all_data)
        
        # 标准化数据
        X_scaled = self.scaler.transform(X_reshaped).reshape(X.shape)
        y_scaled = self.scaler.transform(y_reshaped).reshape(y.shape)
        
        # 按7:3划分数据集
        split_idx = int(len(X) * 0.7)
        
        self.X_train = X_scaled[:split_idx]
        self.X_test = X_scaled[split_idx:]
        self.y_train = y_scaled[:split_idx]
        self.y_test = y_scaled[split_idx:]
        
        print(f"训练集大小: {len(self.X_train)} (70%)")
        print(f"测试集大小: {len(self.X_test)} (30%)")
        
        # 转换为PyTorch张量
        self.X_train = torch.FloatTensor(self.X_train).unsqueeze(-1)  # [batch, seq_len, 1]
        self.X_test = torch.FloatTensor(self.X_test).unsqueeze(-1)
        self.y_train = torch.FloatTensor(self.y_train)  # [batch, pred_len]
        self.y_test = torch.FloatTensor(self.y_test)
        
    def train_model(self, epochs=100, batch_size=32, learning_rate=0.0001):
        """训练模型"""
        print("开始训练完整CALF模型...")
        
        self.model.to(self.device)
        
        # 创建数据加载器
        train_dataset = TensorDataset(self.X_train, self.y_train)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        
        # 优化器和损失函数
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=learning_rate, weight_decay=0.01)
        criterion = nn.MSELoss()
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs, eta_min=1e-6)
        
        # 计算模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"模型总参数数量: {total_params:,}")
        print(f"可训练参数数量: {trainable_params:,}")
        
        # 训练循环
        self.model.train()
        train_losses = []
        best_loss = float('inf')
        patience = 20
        patience_counter = 0
        
        for epoch in range(epochs):
            epoch_loss = 0.0
            batch_count = 0
            
            for batch_x, batch_y in train_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                
                optimizer.zero_grad()
                
                # 前向传播
                outputs = self.model(batch_x)
                
                # 计算损失
                loss = criterion(outputs, batch_y)
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                
                epoch_loss += loss.item()
                batch_count += 1
            
            scheduler.step()
            
            avg_loss = epoch_loss / batch_count
            train_losses.append(avg_loss)
            
            # 早停机制
            if avg_loss < best_loss:
                best_loss = avg_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_calf_model.pth')
            else:
                patience_counter += 1
            
            if (epoch + 1) % 10 == 0:
                print(f"Epoch [{epoch+1}/{epochs}], Loss: {avg_loss:.6f}, LR: {scheduler.get_last_lr()[0]:.6f}")
            
            # 早停
            if patience_counter >= patience:
                print(f"早停触发，在第{epoch+1}轮停止训练")
                break
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_calf_model.pth'))
        print("模型训练完成！")
        return train_losses
        
    def evaluate_model(self):
        """评估模型"""
        print("正在评估模型...")
        
        self.model.eval()
        
        with torch.no_grad():
            # 测试集预测
            X_test_device = self.X_test.to(self.device)
            y_pred_scaled = self.model(X_test_device).cpu().numpy()
            
            # 反标准化
            y_test_original = self.scaler.inverse_transform(
                self.y_test.numpy().reshape(-1, 1)
            ).reshape(self.y_test.shape)
            
            y_pred_original = self.scaler.inverse_transform(
                y_pred_scaled.reshape(-1, 1)
            ).reshape(y_pred_scaled.shape)
        
        # 计算评估指标
        results = {}
        
        print(f"\n=== 完整CALF模型{self.pred_len}天径流预测结果 ===")
        
        # 逐天评估
        for day in range(self.pred_len):
            y_true_day = y_test_original[:, day]
            y_pred_day = y_pred_original[:, day]
            
            mae = mean_absolute_error(y_true_day, y_pred_day)
            mse = mean_squared_error(y_true_day, y_pred_day)
            r2 = r2_score(y_true_day, y_pred_day)
            nse = nash_sutcliffe_efficiency(y_true_day, y_pred_day)
            mape = np.mean(np.abs((y_true_day - y_pred_day) / y_true_day)) * 100
            
            results[f'day_{day+1}'] = {
                'MAE': mae,
                'MSE': mse,
                'R2': r2,
                'NSE': nse,
                'MAPE': mape
            }
            
            print(f"第{day+1}天预测 - R²: {r2:.4f}, NSE: {nse:.4f}, MAE: {mae:.2f}, MAPE: {mape:.1f}%")
        
        # 整体评估
        y_true_all = y_test_original.flatten()
        y_pred_all = y_pred_original.flatten()
        
        overall_mae = mean_absolute_error(y_true_all, y_pred_all)
        overall_mse = mean_squared_error(y_true_all, y_pred_all)
        overall_r2 = r2_score(y_true_all, y_pred_all)
        overall_nse = nash_sutcliffe_efficiency(y_true_all, y_pred_all)
        overall_mape = np.mean(np.abs((y_true_all - y_pred_all) / y_true_all)) * 100
        
        results['overall'] = {
            'MAE': overall_mae,
            'MSE': overall_mse,
            'R2': overall_r2,
            'NSE': overall_nse,
            'MAPE': overall_mape
        }
        
        print(f"\n整体预测 - R²: {overall_r2:.4f}, NSE: {overall_nse:.4f}, MAE: {overall_mae:.2f}, MAPE: {overall_mape:.1f}%")
        
        # 保存结果
        self.results = {
            'daily_results': results,
            'predictions': y_pred_original,
            'actuals': y_test_original
        }
        
        return results

def main():
    """主函数"""
    print("=== 完整CALF径流预测系统 ===")
    print("评价指标: NSE (纳什效率系数) 和 R² (决定系数)")
    print("数据分割: 70%训练集, 30%测试集")
    
    # 创建预测器
    predictor = FullCALFPredictor(
        seq_len=96,      # 输入序列长度：96天
        pred_len=7,      # 预测序列长度：7天
        d_model=768,     # 模型维度
        n_heads=12,      # 注意力头数
        n_layers=6       # Transformer层数
    )
    
    # 执行完整流程
    predictor.load_data()
    predictor.prepare_time_series_data()
    
    # 训练模型
    train_losses = predictor.train_model(epochs=100, batch_size=32, learning_rate=0.0001)
    
    # 评估模型
    results = predictor.evaluate_model()
    
    print("\n=== 实验完成 ===")
    print("模型架构: 完整CALF (大规模Transformer)")
    print("数据分割: 7:3")
    print("预测任务: 7天径流预测")
    print("主要指标: NSE和R²")
    
    return predictor, results

if __name__ == "__main__":
    predictor, results = main()
