"""
简化版7天径流预测演示
专注于核心预测功能，避免复杂可视化
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

class Simple7DayPredictor:
    """简化版7天径流预测器"""
    
    def __init__(self, data_path='1971-2017all_cleaned_CHANGBEI_daily_weather_data.csv'):
        self.data_path = data_path
        self.data = None
        self.models = {}
        self.scaler = None
        
    def load_data(self):
        """加载数据"""
        print("加载数据中...")
        self.data = pd.read_csv(self.data_path)
        self.data['DATE'] = pd.to_datetime(self.data['DATE'])
        self.data = self.data.sort_values('DATE').reset_index(drop=True)
        self.data = self.data.dropna()
        print(f"数据加载完成：{len(self.data)}条记录")
        
    def create_features(self, forecast_days=7):
        """创建特征"""
        print("创建预测特征...")
        
        # 滞后特征
        lag_days = [1, 2, 3, 7, 14, 21, 30]
        for lag in lag_days:
            self.data[f'LAG_{lag}'] = self.data['RUNOFF'].shift(lag)
        
        # 移动平均特征
        windows = [3, 7, 14, 30]
        for window in windows:
            self.data[f'MA_{window}'] = self.data['RUNOFF'].rolling(window=window).mean()
        
        # 时间特征
        self.data['MONTH'] = self.data['DATE'].dt.month
        self.data['DAY_OF_YEAR'] = self.data['DATE'].dt.dayofyear
        
        # 创建目标变量（未来1-7天的径流）
        for day in range(1, forecast_days + 1):
            self.data[f'TARGET_{day}'] = self.data['RUNOFF'].shift(-day)
        
        # 删除缺失值
        self.data = self.data.dropna()
        
        # 定义特征列
        self.feature_cols = [f'LAG_{lag}' for lag in lag_days] + \
                           [f'MA_{window}' for window in windows] + \
                           ['MONTH', 'DAY_OF_YEAR']
        
        self.target_cols = [f'TARGET_{day}' for day in range(1, forecast_days + 1)]
        
        print(f"特征创建完成：{len(self.feature_cols)}个特征")
        
    def train_models(self):
        """训练7个独立的预测模型"""
        print("训练预测模型...")
        
        X = self.data[self.feature_cols]
        
        # 数据分割
        split_idx = int(len(X) * 0.8)
        X_train = X.iloc[:split_idx]
        X_test = X.iloc[split_idx:]
        
        # 标准化
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 保存测试数据
        self.X_test_scaled = X_test_scaled
        self.test_start_idx = split_idx
        
        # 为每一天训练独立模型
        for day in range(1, 8):
            print(f"  训练第{day}天模型...")
            
            y_train = self.data[f'TARGET_{day}'].iloc[:split_idx]
            
            model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42 + day,
                n_jobs=-1
            )
            
            model.fit(X_train_scaled, y_train)
            self.models[f'day_{day}'] = model
        
        print("模型训练完成！")
        
    def predict_7days(self, input_features):
        """预测未来7天径流"""
        if self.scaler is None:
            raise ValueError("模型未训练，请先调用train_models()")
        
        # 标准化输入特征
        input_scaled = self.scaler.transform(input_features.reshape(1, -1))
        
        # 预测每一天
        predictions = []
        for day in range(1, 8):
            model = self.models[f'day_{day}']
            pred = model.predict(input_scaled)[0]
            predictions.append(pred)
        
        return np.array(predictions)
        
    def evaluate_models(self):
        """评估模型性能"""
        print("\n=== 模型评估结果 ===")
        
        # 获取测试集真实值
        test_targets = []
        for day in range(1, 8):
            y_true = self.data[f'TARGET_{day}'].iloc[self.test_start_idx:].values
            test_targets.append(y_true)
        
        # 预测测试集
        test_predictions = []
        for i in range(len(self.X_test_scaled)):
            pred = self.predict_7days(self.X_test_scaled[i])
            test_predictions.append(pred)
        
        test_predictions = np.array(test_predictions)
        
        # 计算每天的评估指标
        results = {}
        for day in range(7):
            y_true = test_targets[day][:len(test_predictions)]
            y_pred = test_predictions[:, day]
            
            mae = mean_absolute_error(y_true, y_pred)
            mse = mean_squared_error(y_true, y_pred)
            r2 = r2_score(y_true, y_pred)
            
            results[f'day_{day+1}'] = {
                'MAE': mae,
                'MSE': mse,
                'R2': r2,
                'relative_error': mae / np.mean(y_true) * 100
            }
            
            print(f"第{day+1}天预测 - MAE: {mae:.2f}, R²: {r2:.4f}, 相对误差: {mae/2214*100:.1f}%")
        
        # 整体评估
        all_true = np.concatenate([test_targets[i][:len(test_predictions)] for i in range(7)])
        all_pred = test_predictions.flatten()
        
        overall_mae = mean_absolute_error(all_true, all_pred)
        overall_r2 = r2_score(all_true, all_pred)
        
        print(f"\n整体预测 - MAE: {overall_mae:.2f}, R²: {overall_r2:.4f}, 相对误差: {overall_mae/2214*100:.1f}%")
        
        return results
        
    def demo_prediction(self):
        """演示预测功能"""
        print("\n=== 预测演示 ===")
        
        # 使用最新的数据进行演示
        latest_data = self.data.iloc[-1]
        input_features = latest_data[self.feature_cols].values
        
        print(f"基于 {latest_data['DATE'].strftime('%Y-%m-%d')} 的数据预测未来7天:")
        print(f"当日径流量: {latest_data['RUNOFF']:.2f}")
        
        # 进行预测
        predictions = self.predict_7days(input_features)
        
        print("\n未来7天预测结果:")
        for i, pred in enumerate(predictions, 1):
            future_date = latest_data['DATE'] + pd.Timedelta(days=i)
            print(f"  第{i}天 ({future_date.strftime('%Y-%m-%d')}): {pred:.2f}")
        
        return predictions
        
    def get_feature_importance(self):
        """获取特征重要性"""
        print("\n=== 特征重要性分析 ===")
        
        # 计算所有模型的平均特征重要性
        importance_sum = np.zeros(len(self.feature_cols))
        
        for day in range(1, 8):
            model = self.models[f'day_{day}']
            importance_sum += model.feature_importances_
        
        avg_importance = importance_sum / 7
        
        # 创建重要性DataFrame
        importance_df = pd.DataFrame({
            'feature': self.feature_cols,
            'importance': avg_importance
        }).sort_values('importance', ascending=False)
        
        print("Top 10 重要特征:")
        for i, row in importance_df.head(10).iterrows():
            print(f"  {row['feature']}: {row['importance']:.4f}")
        
        return importance_df

def main():
    """主函数"""
    print("=== 7天径流预测系统 ===\n")
    
    # 创建预测器
    predictor = Simple7DayPredictor()
    
    # 执行完整流程
    predictor.load_data()
    predictor.create_features()
    predictor.train_models()
    
    # 评估模型
    results = predictor.evaluate_models()
    
    # 特征重要性分析
    importance = predictor.get_feature_importance()
    
    # 演示预测
    predictions = predictor.demo_prediction()
    
    print("\n=== 系统运行完成 ===")
    
    return predictor, results, importance, predictions

if __name__ == "__main__":
    predictor, results, importance, predictions = main()
